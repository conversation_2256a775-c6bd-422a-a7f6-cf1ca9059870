# Sprint 1 开发准备指南

## 概述

Sprint 0已成功完成，基础设施建设达到预期目标。本指南为Sprint 1"核心渠道管理"功能开发提供详细的准备说明和开发指导。

**Sprint 1目标**: 实现史诗1"核心渠道管理"的完整功能
**开发方法**: 严格遵循BDD三步走流程
**预计周期**: 2-3周

## Sprint 1 功能范围

### 核心功能模块

基于已完成的BDD剧本，Sprint 1将实现以下功能：

#### 1. 渠道连接管理
**剧本文件**: `features/core_channel_management/channel_connection.feature`
**核心场景**:
- 成功连接微信账号
- 连接失败处理
- 重复连接检测
- 连接超时处理
- 网络异常处理
- 多平台账号连接
- 权限验证
- 批量连接
- 状态实时更新
- 连接历史记录

#### 2. 渠道管理功能
**剧本文件**: `features/core_channel_management/channel_management.feature`
**核心场景**:
- 设置渠道别名
- 删除渠道连接
- 查看渠道详细信息
- 批量管理渠道
- 渠道排序和筛选
- 渠道状态切换
- 渠道信息导出
- 渠道搜索功能

#### 3. 渠道监控功能
**剧本文件**: `features/core_channel_management/channel_monitoring.feature`
**核心场景**:
- 正常状态监控
- 连接异常检测
- 自动重连机制
- 重连失败处理
- 手动重连操作
- 监控告警配置
- 批量状态检查
- 历史监控数据
- 实时状态推送
- 监控数据统计

## 开发前准备工作

### 1. 环境问题修复

根据Sprint 0验收测试结果，需要先解决以下问题：

#### 修复前端TypeScript类型错误
```bash
cd src/frontend

# 修复API测试mock类型
# 编辑 src/services/api.test.ts
# 使用正确的vitest mock语法
```

#### 安装PostgreSQL驱动
```bash
cd src/backend
pip install psycopg2-binary
pip freeze > requirements.txt
```

#### 配置pytest自定义标记
```bash
# 创建 src/backend/pytest.ini
[tool:pytest]
markers =
    unit: 单元测试标记
    api: API测试标记
    integration: 集成测试标记
```

### 2. 数据库设计

根据BDD剧本需求，设计核心数据表：

#### 渠道表 (channels)
```sql
CREATE TABLE channels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    platform VARCHAR(50) NOT NULL,
    account_id VARCHAR(255) NOT NULL,
    account_name VARCHAR(255),
    alias VARCHAR(255),
    status VARCHAR(50) DEFAULT 'connected',
    connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_active_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(platform, account_id)
);
```

#### 连接历史表 (connection_history)
```sql
CREATE TABLE connection_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    channel_id UUID REFERENCES channels(id),
    action VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 监控记录表 (monitoring_logs)
```sql
CREATE TABLE monitoring_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    channel_id UUID REFERENCES channels(id),
    status VARCHAR(50) NOT NULL,
    response_time INTEGER,
    error_details TEXT,
    checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. API设计

基于BDD剧本设计RESTful API：

#### 渠道连接API
```
POST   /api/v1/channels/connect     # 连接新渠道
GET    /api/v1/channels/connect/qr  # 获取连接二维码
POST   /api/v1/channels/verify      # 验证连接状态
```

#### 渠道管理API
```
GET    /api/v1/channels             # 获取渠道列表
GET    /api/v1/channels/{id}        # 获取渠道详情
PUT    /api/v1/channels/{id}        # 更新渠道信息
DELETE /api/v1/channels/{id}        # 删除渠道
POST   /api/v1/channels/batch       # 批量操作
GET    /api/v1/channels/export      # 导出渠道信息
```

#### 渠道监控API
```
GET    /api/v1/channels/status      # 获取所有渠道状态
POST   /api/v1/channels/check       # 手动检查状态
GET    /api/v1/channels/{id}/logs   # 获取监控日志
POST   /api/v1/channels/{id}/reconnect # 手动重连
```

## BDD开发流程

### 第一步：编写自动化测试 (1周)

基于已有的Gherkin剧本，编写对应的自动化测试：

#### 1.1 后端API测试
```bash
# 创建测试文件
tests/integration/test_channel_connection.py
tests/integration/test_channel_management.py
tests/integration/test_channel_monitoring.py

# 运行测试 (应该失败)
cd src/backend
pytest tests/integration/ -v
```

#### 1.2 前端组件测试
```bash
# 创建测试文件
src/frontend/src/components/ChannelConnection.test.tsx
src/frontend/src/components/ChannelManagement.test.tsx
src/frontend/src/components/ChannelMonitoring.test.tsx

# 运行测试 (应该失败)
cd src/frontend
npm run test:run
```

#### 1.3 E2E测试
```bash
# 创建测试文件
tests/e2e/channel-connection.spec.ts
tests/e2e/channel-management.spec.ts
tests/e2e/channel-monitoring.spec.ts

# 运行测试 (应该失败)
cd tests/e2e
npm run test
```

### 第二步：编写产品代码 (1-2周)

#### 2.1 后端实现
```bash
# 实现数据模型
src/backend/app/models/channel.py

# 实现业务服务
src/backend/app/services/channel_service.py
src/backend/app/services/monitoring_service.py

# 实现API端点
src/backend/app/api/v1/channels.py

# 实现数据库迁移
src/backend/alembic/versions/001_create_channels.py
```

#### 2.2 前端实现
```bash
# 实现页面组件
src/frontend/src/pages/ChannelManagement.tsx

# 实现功能组件
src/frontend/src/components/ChannelConnection.tsx
src/frontend/src/components/ChannelList.tsx
src/frontend/src/components/ChannelMonitoring.tsx

# 实现API服务
src/frontend/src/services/channelApi.ts

# 实现状态管理
src/frontend/src/stores/channelStore.ts
```

### 第三步：测试验证与重构

#### 3.1 确保所有测试通过
```bash
# 后端测试
cd src/backend && pytest tests/ --cov=app

# 前端测试
cd src/frontend && npm run test:coverage

# E2E测试
cd tests/e2e && npm run test
```

#### 3.2 代码质量检查
```bash
# 运行完整质量检查
./scripts/check-quality.sh

# 确保CI通过
git push origin feature/core-channel-management
```

## 开发最佳实践

### 1. 严格遵循BDD流程
- 每个功能都必须先有失败的测试
- 编写刚好让测试通过的最简代码
- 重构时保持测试通过

### 2. 小步快跑
- 每个Scenario单独实现
- 频繁提交，保持小的变更集
- 及时运行CI验证

### 3. 质量保证
- 使用开发脚本进行本地验证
- 保持测试覆盖率 ≥ 85%
- 遵循代码规范和命名约定

### 4. 文档同步
- 及时更新API文档
- 记录重要的设计决策
- 维护用户使用指南

## 风险与应对

### 技术风险
1. **第三方平台API集成复杂**
   - 应对：先实现Mock版本，后续集成真实API
   
2. **实时监控性能问题**
   - 应对：使用WebSocket + 合理的轮询频率
   
3. **数据库性能瓶颈**
   - 应对：合理设计索引，使用连接池

### 进度风险
1. **功能范围过大**
   - 应对：按优先级分批实现，核心功能优先
   
2. **测试编写耗时**
   - 应对：并行开发，复用测试工具和模式

## 成功标准

Sprint 1完成的标准：

### 功能完整性
- ✅ 所有BDD剧本场景都有对应的自动化测试
- ✅ 所有测试都通过
- ✅ 核心功能在浏览器中可以正常使用

### 质量标准
- ✅ 代码覆盖率 ≥ 85%
- ✅ 所有CI检查通过
- ✅ 性能测试满足要求

### 交付标准
- ✅ 功能部署到测试环境
- ✅ 用户验收测试通过
- ✅ 技术文档更新完整

---

**准备完成检查清单**:
- [ ] 环境问题修复完成
- [ ] 数据库设计确认
- [ ] API设计评审通过
- [ ] 开发环境验证正常
- [ ] 团队对BDD流程达成共识

**Sprint 1开始条件**: 完成上述检查清单后即可开始开发

---

**文档维护**: 本指南将在Sprint 1开发过程中持续更新
**下次更新**: Sprint 1第一周结束时
