#!/bin/bash

# 本地代码质量检查脚本
# 按照Sprint 0开发方案的简化CI要求

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 记录开始时间
start_time=$(date +%s)

echo "🔍 运行本地代码质量检查..."

# 后端检查
log_info "检查后端代码..."
cd src/backend

# 检查虚拟环境
if [ -d "venv" ]; then
    source venv/bin/activate
    log_info "已激活虚拟环境"
fi

# 代码格式化
log_info "格式化后端代码..."
black app/
isort app/

# 运行基础测试
log_info "运行后端测试..."
pytest tests/ -v --tb=short

log_success "后端检查完成"

# 前端检查
log_info "检查前端代码..."
cd ../frontend

# 代码检查和修复
log_info "检查前端代码规范..."
npm run lint:fix 2>/dev/null || npm run lint || log_warning "前端代码需要手动修复"

# 跳过TypeScript检查（简化CI）
log_info "跳过TypeScript类型检查（简化CI模式）"

# 构建测试（非阻塞）
log_info "测试前端构建..."
if npm run build 2>/dev/null; then
    log_success "前端构建成功"
else
    log_warning "前端构建有警告，但不阻塞提交"
fi

log_success "前端检查完成"

# 返回根目录
cd ../..

# 计算耗时
end_time=$(date +%s)
duration=$((end_time - start_time))

log_success "✅ 质量检查完成！耗时: ${duration}秒"

echo ""
echo "📋 检查总结:"
echo "  - 后端代码已格式化"
echo "  - 后端测试已通过"
echo "  - 前端代码已检查"
echo "  - 前端构建已验证"
echo ""
echo "💡 提示: 现在可以安全提交代码了！"
