#!/bin/bash

# 快速检查脚本 - 简化版本
# 按照Sprint 0开发方案的快速反馈原则

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo "⚡ 快速代码检查..."

# 后端快速检查
log_info "后端代码格式化..."
cd src/backend
black app/ --quiet
isort app/ --quiet

# 前端快速检查
log_info "前端代码检查..."
cd ../frontend
npm run lint:fix >/dev/null 2>&1 || true

# 返回根目录
cd ../..

log_success "✅ 快速检查完成！代码已格式化"

echo ""
echo "💡 提示:"
echo "  - 运行 './scripts/check-quality.sh' 进行完整检查"
echo "  - 运行 './scripts/quick-commit.sh' 快速提交"
