#!/bin/bash

# 快速提交脚本
# 按照Sprint 0开发方案的个人开发工作流

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 准备提交代码..."

# 检查是否有未提交的更改
if [ -z "$(git status --porcelain)" ]; then
    log_info "没有需要提交的更改"
    exit 0
fi

# 显示当前状态
log_info "当前更改:"
git status --short

echo ""
read -p "📝 请输入提交信息: " commit_message

if [ -z "$commit_message" ]; then
    log_error "提交信息不能为空"
    exit 1
fi

# 运行质量检查
log_info "运行代码质量检查..."
if ./scripts/check-quality.sh; then
    log_success "质量检查通过"
else
    log_error "质量检查失败，请修复后再提交"
    exit 1
fi

# 提交代码
log_info "提交代码..."
git add .
git commit -m "$commit_message"

log_success "✅ 代码已提交！"

# 询问是否推送
echo ""
read -p "🌐 是否推送到远程仓库? (y/N): " push_confirm

if [[ $push_confirm =~ ^[Yy]$ ]]; then
    log_info "推送到远程仓库..."
    git push
    log_success "✅ 代码已推送！"
    
    echo ""
    log_info "🔗 GitHub Actions将自动运行CI检查"
    log_info "📊 查看CI状态: https://github.com/$(git config --get remote.origin.url | sed 's/.*github.com[:/]\([^.]*\).*/\1/')/actions"
else
    log_info "代码已本地提交，稍后可手动推送"
fi

echo ""
log_success "🎉 提交完成！继续愉快地开发吧！"
