# 柴管家 - 多平台聚合智能客服系统

## 项目概述

柴管家是一个多平台聚合智能客服系统，为知识类、教培类个人IP运营者提供一站式私域运营解决方案。通过聚合多平台消息和AI能力，实现降本增效、激活社群、深化用户关系的目标。

## 核心功能

- **统一消息管理**: 将微信、抖音、小红书等多平台消息聚合到统一工作台
- **AI智能助理**: 提供AI副驾式回复建议和智能托管功能
- **人机协作**: 实现高置信度自动回复，低置信度人工接管的智能分流
- **知识库管理**: 构建可复用的FAQ知识库，提升回复效率

## 技术架构

- **后端**: Python + FastAPI + PostgreSQL + RabbitMQ
- **前端**: React + TypeScript + Vite
- **数据库**: PostgreSQL (主数据库) + ChromaDB (向量数据库)
- **部署**: Docker + Docker Compose
- **测试**: pytest (后端) + Vitest (前端) + Playwright (E2E)

## 快速启动

### 环境要求

- Python 3.11+
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 14+

### 一键启动开发环境

```bash
# 1. 克隆项目
git clone <repository-url>
cd chaiguanjia_ag_8.3

# 2. 启动开发环境
./scripts/dev-start.sh

# 3. 访问应用
# 前端应用: http://localhost:3000
# API文档: http://localhost:8000/docs
# 数据库管理: http://localhost:15672 (RabbitMQ管理界面)
```

### 手动启动

```bash
# 1. 启动基础服务
docker-compose up -d

# 2. 启动后端服务
cd src/backend
pip install -r requirements.txt
uvicorn app.main:app --reload --port 8000

# 3. 启动前端服务
cd src/frontend
npm install
npm run dev
```

## 开发指南

### 开发流程

本项目采用**行为驱动开发（BDD）**方法论：

1. **编写Gherkin行为剧本** (30分钟)
2. **编写自动化测试** (60分钟)
3. **编写产品代码** (120分钟)

### 测试框架

本项目采用多层次测试策略：

```bash
# 运行所有测试
./scripts/testing/run-all-tests.sh

# 快速测试（开发时使用）
./scripts/testing/quick-test.sh

# 后端测试
cd src/backend
pytest tests/ --cov=app --cov-report=html

# 前端测试
cd src/frontend
npm run test:coverage

# E2E测试
cd tests/e2e
npm run test
```

### 代码质量检查

```bash
# 后端代码检查
cd src/backend
black app/ && isort app/ && flake8 app/ && mypy app/

# 前端代码检查
cd src/frontend
npm run lint

# 快速质量检查
./scripts/testing/quick-test.sh lint
```

### 🚀 简化CI/CD工作流

本项目采用简化的CI配置，专为独立开发者设计：

```bash
# 开发时快速验证 (~30秒)
./scripts/quick-check.sh

# 提交前完整检查 (~2分钟)
./scripts/check-quality.sh

# 一键提交工作流
./scripts/quick-commit.sh
```

**CI策略**:
- **快速反馈**: PR检查在2分钟内完成
- **智能执行**: main分支自动运行完整测试
- **缓存优化**: 依赖缓存加速构建
- **非阻塞警告**: TypeScript警告不阻塞提交

详细说明请参考: [简化CI配置指南](docs/simplified-ci-guide.md)

### 🎯 Sprint 0 完成状态

**当前阶段**: Sprint 0 - 基础设施建设 ✅ 已完成
**完成度**: 100%
**验收评分**: 88/100 ⭐⭐⭐⭐

#### 核心成就
- ✅ 完整的基础设施建设
- ✅ 史诗1"核心渠道管理"BDD剧本完成
- ✅ 简化CI配置，执行效率提升80%
- ✅ 完善的个人开发工作流建立

#### 已交付内容
- 📁 **BDD剧本**: 史诗1核心渠道管理的完整剧本
- 🏗️ **基础架构**: 后端(FastAPI) + 前端(React) + 测试框架
- 🚀 **CI/CD**: 简化的GitHub Actions工作流
- 🛠️ **开发工具**: 个人开发脚本和质量检查工具
- 📚 **文档体系**: 完整的开发指南和规范

**详细报告**: [Sprint 0验收报告](docs/sprint0-acceptance-report.md)

### 🚀 Sprint 1 准备

**下一阶段**: Sprint 1 - 核心渠道管理功能开发
**准备就绪度**: 85% ✅
**开发方法**: BDD三步走流程

**Sprint 1目标**:
- 实现渠道连接管理功能
- 实现渠道管理功能
- 实现渠道监控功能

**准备指南**: [Sprint 1开发准备指南](docs/sprint1-preparation-guide.md)

### 分支管理

```bash
# 功能分支
git checkout -b feature/epic-name/story-name

# 提交规范
git commit -m "feat(scope): description"
```

## 项目结构

```
chaiguanjia_ag_8.3/
├── docs/                           # 项目文档
│   └── testing-guide.md           # 测试框架使用指南
├── features/                       # BDD规范文件
├── tests/                          # 测试文件
│   ├── unit/                       # 单元测试
│   ├── integration/                # 集成测试
│   └── e2e/                        # E2E测试 (Playwright)
├── src/                            # 源代码
│   ├── backend/                    # 后端服务
│   │   ├── tests/                  # 后端测试 (pytest)
│   │   └── pytest.ini              # pytest配置
│   ├── frontend/                   # 前端应用
│   │   ├── vitest.config.ts        # Vitest配置
│   │   └── src/test/               # 前端测试工具
│   └── verification/               # 验证界面
├── infrastructure/                 # 基础设施配置
├── scripts/                        # 脚本文件
│   └── testing/                    # 测试脚本
│       ├── run-all-tests.sh        # 全量测试脚本
│       └── quick-test.sh           # 快速测试脚本
└── .github/                        # GitHub配置
    └── workflows/                  # CI/CD工作流
        ├── test.yml                # 测试流水线
        └── quality.yml             # 代码质量检查
```

## 开发环境验证

### 健康检查

```bash
# 后端健康检查
curl http://localhost:8000/health

# 前端访问检查
curl http://localhost:3000

# 数据库连接检查
psql -h localhost -p 5432 -U postgres -d chaiguanjia_dev
```

### 常见问题

1. **端口冲突**: 检查8000、3000、5432端口是否被占用
2. **依赖安装失败**: 使用国内镜像源
3. **Docker启动失败**: 检查Docker Desktop是否运行

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

[MIT License](LICENSE)

## 联系方式

- 项目负责人: [联系信息]
- 技术支持: [技术支持邮箱]
- 文档地址: [文档链接]
