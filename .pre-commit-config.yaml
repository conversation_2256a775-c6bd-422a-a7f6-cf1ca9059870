# 简化的pre-commit配置
# 按照Sprint 0开发方案的个人开发质量保证体系

repos:
  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.11
        files: ^src/backend/

  # Python导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        files: ^src/backend/

  # 基础文件检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-merge-conflict

  # 前端代码检查（如果有eslint配置）
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.44.0
    hooks:
      - id: eslint
        files: ^src/frontend/.*\.(js|ts|jsx|tsx)$
        types: [file]
        additional_dependencies:
          - eslint@8.44.0
