import{M as K,n as Be,a as xt,c as z,p as ve,i as Le,b as Mt,t as Ct,d as En,v as Rn,e as Kn,f as Nn,g as kn,h as Bn,j as N,k as Ln,l as _n,m as ce,o as tt,r as Pt,q as Un,s as Gn,u as jn,w as Ft,x as Wn,y as $n,z as Hn,A as zn,B as qn,C as Yn,D as Dt,E as It,F as Ot,G as Et}from"./index-B_cn8FyA.js";function Xn(t,e){t.indexOf(e)===-1&&t.push(e)}function Zn(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}let _e=()=>{};const Rt=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Kt=t=>/^0[^.\s]+$/u.test(t);function Ue(t){let e;return()=>(e===void 0&&(e=t()),e)}const q=t=>t,Jn=(t,e)=>n=>e(t(n)),he=(...t)=>t.reduce(Jn),Nt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s};class kt{constructor(){this.subscriptions=[]}add(e){return Xn(this.subscriptions,e),()=>Zn(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let r=0;r<i;r++){const o=this.subscriptions[r];o&&o(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const E=t=>t*1e3,R=t=>t/1e3;function Bt(t,e){return e?t*(1e3/e):0}const Lt=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,Qn=1e-7,es=12;function ts(t,e,n,s,i){let r,o,a=0;do o=e+(n-e)/2,r=Lt(o,s,i)-t,r>0?n=o:e=o;while(Math.abs(r)>Qn&&++a<es);return o}function te(t,e,n,s){if(t===e&&n===s)return q;const i=r=>ts(r,0,1,t,n);return r=>r===0||r===1?r:Lt(i(r),e,s)}const _t=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Ut=t=>e=>1-t(1-e),Gt=te(.33,1.53,.69,.99),Ge=Ut(Gt),jt=_t(Ge),Wt=t=>(t*=2)<1?.5*Ge(t):.5*(2-Math.pow(2,-10*(t-1))),je=t=>1-Math.sin(Math.acos(t)),ns=Ut(je),$t=_t(je),ss=te(.42,0,1,1),is=te(0,0,.58,1),Ht=te(.42,0,.58,1),rs=t=>Array.isArray(t)&&typeof t[0]!="number",zt=t=>Array.isArray(t)&&typeof t[0]=="number",os={linear:q,easeIn:ss,easeInOut:Ht,easeOut:is,circIn:je,circInOut:$t,circOut:ns,backIn:Ge,backInOut:jt,backOut:Gt,anticipate:Wt},as=t=>typeof t=="string",nt=t=>{if(zt(t)){_e(t.length===4);const[e,n,s,i]=t;return te(e,n,s,i)}else if(as(t))return os[t];return t},ne=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function us(t,e){let n=new Set,s=new Set,i=!1,r=!1;const o=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function u(l){o.has(l)&&(c.schedule(l),t()),l(a)}const c={schedule:(l,h=!1,f=!1)=>{const v=f&&i?n:s;return h&&o.add(l),v.has(l)||v.add(l),l},cancel:l=>{s.delete(l),o.delete(l)},process:l=>{if(a=l,i){r=!0;return}i=!0,[n,s]=[s,n],n.forEach(u),n.clear(),i=!1,r&&(r=!1,c.process(l))}};return c}const ls=40;function qt(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},r=()=>n=!0,o=ne.reduce((g,w)=>(g[w]=us(r),g),{}),{setup:a,read:u,resolveKeyframes:c,preUpdate:l,update:h,preRender:f,render:d,postRender:v}=o,T=()=>{const g=K.useManualTiming?i.timestamp:performance.now();n=!1,K.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(g-i.timestamp,ls),1)),i.timestamp=g,i.isProcessing=!0,a.process(i),u.process(i),c.process(i),l.process(i),h.process(i),f.process(i),d.process(i),v.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(T))},b=()=>{n=!0,s=!0,i.isProcessing||t(T)};return{schedule:ne.reduce((g,w)=>{const m=o[w];return g[w]=(A,M=!1,y=!1)=>(n||b(),m.schedule(A,M,y)),g},{}),cancel:g=>{for(let w=0;w<ne.length;w++)o[ne[w]].cancel(g)},state:i,steps:o}}const{schedule:O,cancel:Te,state:re}=qt(typeof requestAnimationFrame<"u"?requestAnimationFrame:q,!0);let se;function cs(){se=void 0}const I={now:()=>(se===void 0&&I.set(re.isProcessing||K.useManualTiming?re.timestamp:performance.now()),se),set:t=>{se=t,queueMicrotask(cs)}},J=t=>Math.round(t*1e5)/1e5,We=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function hs(t){return t==null}const fs=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,$e=(t,e)=>n=>!!(typeof n=="string"&&fs.test(n)&&n.startsWith(t)||e&&!hs(n)&&Object.prototype.hasOwnProperty.call(n,e)),Yt=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,r,o,a]=s.match(We);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},ds=t=>z(0,255,t),de={...Be,transform:t=>Math.round(ds(t))},L={test:$e("rgb","red"),parse:Yt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+de.transform(t)+", "+de.transform(e)+", "+de.transform(n)+", "+J(xt.transform(s))+")"};function ps(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const Ve={test:$e("#"),parse:ps,transform:L.transform},$={test:$e("hsl","hue"),parse:Yt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+ve.transform(J(e))+", "+ve.transform(J(n))+", "+J(xt.transform(s))+")"},x={test:t=>L.test(t)||Ve.test(t)||$.test(t),parse:t=>L.test(t)?L.parse(t):$.test(t)?$.parse(t):Ve.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?L.transform(t):$.transform(t),getAnimatableNone:t=>{const e=x.parse(t);return e.alpha=0,x.transform(e)}},ms=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function gs(t){return isNaN(t)&&typeof t=="string"&&(t.match(We)?.length||0)+(t.match(ms)?.length||0)>0}const Xt="number",Zt="color",ys="var",bs="var(",st="${}",vs=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function ee(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const a=e.replace(vs,u=>(x.test(u)?(s.color.push(r),i.push(Zt),n.push(x.parse(u))):u.startsWith(bs)?(s.var.push(r),i.push(ys),n.push(u)):(s.number.push(r),i.push(Xt),n.push(parseFloat(u))),++r,st)).split(st);return{values:n,split:a,indexes:s,types:i}}function Jt(t){return ee(t).values}function Qt(t){const{split:e,types:n}=ee(t),s=e.length;return i=>{let r="";for(let o=0;o<s;o++)if(r+=e[o],i[o]!==void 0){const a=n[o];a===Xt?r+=J(i[o]):a===Zt?r+=x.transform(i[o]):r+=i[o]}return r}}const Ts=t=>typeof t=="number"?0:x.test(t)?x.getAnimatableNone(t):t;function Vs(t){const e=Jt(t);return Qt(t)(e.map(Ts))}const Y={test:gs,parse:Jt,createTransformer:Qt,getAnimatableNone:Vs};function pe(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function As({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,r=0,o=0;if(!e)i=r=o=n;else{const a=n<.5?n*(1+e):n+e-n*e,u=2*n-a;i=pe(u,a,t+1/3),r=pe(u,a,t),o=pe(u,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(r*255),blue:Math.round(o*255),alpha:s}}function oe(t,e){return n=>n>0?e:t}const fe=(t,e,n)=>t+(e-t)*n,me=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},ws=[Ve,L,$],Ss=t=>ws.find(e=>e.test(t));function it(t){const e=Ss(t);if(!e)return!1;let n=e.parse(t);return e===$&&(n=As(n)),n}const rt=(t,e)=>{const n=it(t),s=it(e);if(!n||!s)return oe(t,e);const i={...n};return r=>(i.red=me(n.red,s.red,r),i.green=me(n.green,s.green,r),i.blue=me(n.blue,s.blue,r),i.alpha=fe(n.alpha,s.alpha,r),L.transform(i))},Ae=new Set(["none","hidden"]);function xs(t,e){return Ae.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function Ms(t,e){return n=>fe(t,e,n)}function He(t){return typeof t=="number"?Ms:typeof t=="string"?Le(t)?oe:x.test(t)?rt:Fs:Array.isArray(t)?en:typeof t=="object"?x.test(t)?rt:Cs:oe}function en(t,e){const n=[...t],s=n.length,i=t.map((r,o)=>He(r)(r,e[o]));return r=>{for(let o=0;o<s;o++)n[o]=i[o](r);return n}}function Cs(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=He(t[i])(t[i],e[i]));return i=>{for(const r in s)n[r]=s[r](i);return n}}function Ps(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const r=e.types[i],o=t.indexes[r][s[r]],a=t.values[o]??0;n[i]=a,s[r]++}return n}const Fs=(t,e)=>{const n=Y.createTransformer(e),s=ee(t),i=ee(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?Ae.has(t)&&!i.values.length||Ae.has(e)&&!s.values.length?xs(t,e):he(en(Ps(s,i),i.values),n):oe(t,e)};function tn(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?fe(t,e,n):He(t)(t,e)}const Ds=t=>{const e=({timestamp:n})=>t(n);return{start:(n=!0)=>O.update(e,n),stop:()=>Te(e),now:()=>re.isProcessing?re.timestamp:I.now()}},nn=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let r=0;r<i;r++)s+=Math.round(t(r/(i-1))*1e4)/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},ae=2e4;function ze(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<ae;)e+=n,s=t.next(e);return e>=ae?1/0:e}function Is(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(ze(s),ae);return{type:"keyframes",ease:r=>s.next(i*r).value/e,duration:R(i)}}const Os=5;function sn(t,e,n){const s=Math.max(e-Os,0);return Bt(n-t(s),e-s)}const S={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},ge=.001;function Es({duration:t=S.duration,bounce:e=S.bounce,velocity:n=S.velocity,mass:s=S.mass}){let i,r,o=1-e;o=z(S.minDamping,S.maxDamping,o),t=z(S.minDuration,S.maxDuration,R(t)),o<1?(i=c=>{const l=c*o,h=l*t,f=l-n,d=we(c,o),v=Math.exp(-h);return ge-f/d*v},r=c=>{const h=c*o*t,f=h*n+n,d=Math.pow(o,2)*Math.pow(c,2)*t,v=Math.exp(-h),T=we(Math.pow(c,2),o);return(-i(c)+ge>0?-1:1)*((f-d)*v)/T}):(i=c=>{const l=Math.exp(-c*t),h=(c-n)*t+1;return-ge+l*h},r=c=>{const l=Math.exp(-c*t),h=(n-c)*(t*t);return l*h});const a=5/t,u=Ks(i,r,a);if(t=E(t),isNaN(u))return{stiffness:S.stiffness,damping:S.damping,duration:t};{const c=Math.pow(u,2)*s;return{stiffness:c,damping:o*2*Math.sqrt(s*c),duration:t}}}const Rs=12;function Ks(t,e,n){let s=n;for(let i=1;i<Rs;i++)s=s-t(s)/e(s);return s}function we(t,e){return t*Math.sqrt(1-e*e)}const Ns=["duration","bounce"],ks=["stiffness","damping","mass"];function ot(t,e){return e.some(n=>t[n]!==void 0)}function Bs(t){let e={velocity:S.velocity,stiffness:S.stiffness,damping:S.damping,mass:S.mass,isResolvedFromDuration:!1,...t};if(!ot(t,ks)&&ot(t,Ns))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,r=2*z(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:S.mass,stiffness:i,damping:r}}else{const n=Es(t);e={...e,...n,mass:S.mass},e.isResolvedFromDuration=!0}return e}function ue(t=S.visualDuration,e=S.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const r=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],a={done:!1,value:r},{stiffness:u,damping:c,mass:l,duration:h,velocity:f,isResolvedFromDuration:d}=Bs({...n,velocity:-R(n.velocity||0)}),v=f||0,T=c/(2*Math.sqrt(u*l)),b=o-r,p=R(Math.sqrt(u/l)),V=Math.abs(b)<5;s||(s=V?S.restSpeed.granular:S.restSpeed.default),i||(i=V?S.restDelta.granular:S.restDelta.default);let g;if(T<1){const m=we(p,T);g=A=>{const M=Math.exp(-T*p*A);return o-M*((v+T*p*b)/m*Math.sin(m*A)+b*Math.cos(m*A))}}else if(T===1)g=m=>o-Math.exp(-p*m)*(b+(v+p*b)*m);else{const m=p*Math.sqrt(T*T-1);g=A=>{const M=Math.exp(-T*p*A),y=Math.min(m*A,300);return o-M*((v+T*p*b)*Math.sinh(y)+m*b*Math.cosh(y))/m}}const w={calculatedDuration:d&&h||null,next:m=>{const A=g(m);if(d)a.done=m>=h;else{let M=m===0?v:0;T<1&&(M=m===0?E(v):sn(g,m,A));const y=Math.abs(M)<=s,P=Math.abs(o-A)<=i;a.done=y&&P}return a.value=a.done?o:A,a},toString:()=>{const m=Math.min(ze(w),ae),A=nn(M=>w.next(m*M).value,m,30);return m+"ms "+A},toTransition:()=>{}};return w}ue.applyToOptions=t=>{const e=Is(t,100,ue);return t.ease=e.ease,t.duration=E(e.duration),t.type="keyframes",t};function Se({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:u,restDelta:c=.5,restSpeed:l}){const h=t[0],f={done:!1,value:h},d=y=>a!==void 0&&y<a||u!==void 0&&y>u,v=y=>a===void 0?u:u===void 0||Math.abs(a-y)<Math.abs(u-y)?a:u;let T=n*e;const b=h+T,p=o===void 0?b:o(b);p!==b&&(T=p-h);const V=y=>-T*Math.exp(-y/s),g=y=>p+V(y),w=y=>{const P=V(y),F=g(y);f.done=Math.abs(P)<=c,f.value=f.done?p:F};let m,A;const M=y=>{d(f.value)&&(m=y,A=ue({keyframes:[f.value,v(f.value)],velocity:sn(g,y,f.value),damping:i,stiffness:r,restDelta:c,restSpeed:l}))};return M(0),{calculatedDuration:null,next:y=>{let P=!1;return!A&&m===void 0&&(P=!0,w(y),M(y)),m!==void 0&&y>=m?A.next(y-m):(!P&&w(y),f)}}}function Ls(t,e,n){const s=[],i=n||K.mix||tn,r=t.length-1;for(let o=0;o<r;o++){let a=i(t[o],t[o+1]);if(e){const u=Array.isArray(e)?e[o]||q:e;a=he(u,a)}s.push(a)}return s}function _s(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const r=t.length;if(_e(r===e.length),r===1)return()=>e[0];if(r===2&&e[0]===e[1])return()=>e[1];const o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=Ls(e,s,i),u=a.length,c=l=>{if(o&&l<t[0])return e[0];let h=0;if(u>1)for(;h<t.length-2&&!(l<t[h+1]);h++);const f=Nt(t[h],t[h+1],l);return a[h](f)};return n?l=>c(z(t[0],t[r-1],l)):c}function Us(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=Nt(0,e,s);t.push(fe(n,1,i))}}function Gs(t){const e=[0];return Us(e,t.length-1),e}function js(t,e){return t.map(n=>n*e)}function Ws(t,e){return t.map(()=>e||Ht).splice(0,t.length-1)}function Q({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=rs(s)?s.map(nt):nt(s),r={done:!1,value:e[0]},o=js(n&&n.length===e.length?n:Gs(e),t),a=_s(o,e,{ease:Array.isArray(i)?i:Ws(e,i)});return{calculatedDuration:t,next:u=>(r.value=a(u),r.done=u>=t,r)}}const $s=t=>t!==null;function qe(t,{repeat:e,repeatType:n="loop"},s,i=1){const r=t.filter($s),a=i<0||e&&n!=="loop"&&e%2===1?0:r.length-1;return!a||s===void 0?r[a]:s}const Hs={decay:Se,inertia:Se,tween:Q,keyframes:Q,spring:ue};function rn(t){typeof t.type=="string"&&(t.type=Hs[t.type])}class Ye{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,n){return this.finished.then(e,n)}}const zs=t=>t/100;class Xe extends Ye{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:n}=this.options;n&&n.updatedAt!==I.now()&&this.tick(I.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),this.options.onStop?.())},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;rn(e);const{type:n=Q,repeat:s=0,repeatDelay:i=0,repeatType:r,velocity:o=0}=e;let{keyframes:a}=e;const u=n||Q;u!==Q&&typeof a[0]!="number"&&(this.mixKeyframes=he(zs,tn(a[0],a[1])),a=[0,100]);const c=u({...e,keyframes:a});r==="mirror"&&(this.mirroredGenerator=u({...e,keyframes:[...a].reverse(),velocity:-o})),c.calculatedDuration===null&&(c.calculatedDuration=ze(c));const{calculatedDuration:l}=c;this.calculatedDuration=l,this.resolvedDuration=l+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=c}updateTime(e){const n=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(e,n=!1){const{generator:s,totalDuration:i,mixKeyframes:r,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:u}=this;if(this.startTime===null)return s.next(0);const{delay:c=0,keyframes:l,repeat:h,repeatType:f,repeatDelay:d,type:v,onUpdate:T,finalKeyframe:b}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-i/this.speed,this.startTime)),n?this.currentTime=e:this.updateTime(e);const p=this.currentTime-c*(this.playbackSpeed>=0?1:-1),V=this.playbackSpeed>=0?p<0:p>i;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=i);let g=this.currentTime,w=s;if(h){const y=Math.min(this.currentTime,i)/a;let P=Math.floor(y),F=y%1;!F&&y>=1&&(F=1),F===1&&P--,P=Math.min(P,h+1),!!(P%2)&&(f==="reverse"?(F=1-F,d&&(F-=d/a)):f==="mirror"&&(w=o)),g=z(0,1,F)*a}const m=V?{done:!1,value:l[0]}:w.next(g);r&&(m.value=r(m.value));let{done:A}=m;!V&&u!==null&&(A=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const M=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&A);return M&&v!==Se&&(m.value=qe(l,this.options,b,this.speed)),T&&T(m.value),M&&this.finish(),m}then(e,n){return this.finished.then(e,n)}get duration(){return R(this.calculatedDuration)}get time(){return R(this.currentTime)}set time(e){e=E(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(I.now());const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=R(this.currentTime))}play(){if(this.isStopped)return;const{driver:e=Ds,startTime:n}=this.options;this.driver||(this.driver=e(i=>this.tick(i))),this.options.onPlay?.();const s=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=s):this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime||(this.startTime=n??s),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(I.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}function qs(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const _=t=>t*180/Math.PI,xe=t=>{const e=_(Math.atan2(t[1],t[0]));return Me(e)},Ys={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:xe,rotateZ:xe,skewX:t=>_(Math.atan(t[1])),skewY:t=>_(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Me=t=>(t=t%360,t<0&&(t+=360),t),at=xe,ut=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),lt=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Xs={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ut,scaleY:lt,scale:t=>(ut(t)+lt(t))/2,rotateX:t=>Me(_(Math.atan2(t[6],t[5]))),rotateY:t=>Me(_(Math.atan2(-t[2],t[0]))),rotateZ:at,rotate:at,skewX:t=>_(Math.atan(t[4])),skewY:t=>_(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Ce(t){return t.includes("scale")?1:0}function Pe(t,e){if(!t||t==="none")return Ce(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=Xs,i=n;else{const a=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=Ys,i=a}if(!i)return Ce(e);const r=s[e],o=i[1].split(",").map(Js);return typeof r=="function"?r(o):o[r]}const Zs=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Pe(n,e)};function Js(t){return parseFloat(t.trim())}const ct=t=>t===Be||t===Mt,Qs=new Set(["x","y","z"]),ei=Ct.filter(t=>!Qs.has(t));function ti(t){const e=[];return ei.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const U={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Pe(e,"x"),y:(t,{transform:e})=>Pe(e,"y")};U.translateX=U.x;U.translateY=U.y;const G=new Set;let Fe=!1,De=!1,Ie=!1;function on(){if(De){const t=Array.from(G).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=ti(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([r,o])=>{s.getValue(r)?.set(o)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}De=!1,Fe=!1,G.forEach(t=>t.complete(Ie)),G.clear()}function an(){G.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(De=!0)})}function ni(){Ie=!0,an(),on(),Ie=!1}class Ze{constructor(e,n,s,i,r,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=r,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(G.add(this),Fe||(Fe=!0,O.read(an),O.resolveKeyframes(on))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;if(e[0]===null){const r=i?.get(),o=e[e.length-1];if(r!==void 0)e[0]=r;else if(s&&n){const a=s.readValue(n,o);a!=null&&(e[0]=a)}e[0]===void 0&&(e[0]=o),i&&r===void 0&&i.set(e[0])}qs(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),G.delete(this)}cancel(){this.state==="scheduled"&&(G.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const si=t=>t.startsWith("--");function ii(t,e,n){si(e)?t.style.setProperty(e,n):t.style[e]=n}const ri=Ue(()=>window.ScrollTimeline!==void 0),oi={};function ai(t,e){const n=Ue(t);return()=>oi[e]??n()}const un=ai(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Z=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,ht={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Z([0,.65,.55,1]),circOut:Z([.55,0,1,.45]),backIn:Z([.31,.01,.66,-.59]),backOut:Z([.33,1.53,.69,.99])};function ln(t,e){if(t)return typeof t=="function"?un()?nn(t,e):"ease-out":zt(t)?Z(t):Array.isArray(t)?t.map(n=>ln(n,e)||ht.easeOut):ht[t]}function ui(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:u}={},c=void 0){const l={[e]:n};u&&(l.offset=u);const h=ln(a,i);Array.isArray(h)&&(l.easing=h);const f={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:o==="reverse"?"alternate":"normal"};return c&&(f.pseudoElement=c),t.animate(l,f)}function cn(t){return typeof t=="function"&&"applyToOptions"in t}function li({type:t,...e}){return cn(t)&&un()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class ci extends Ye{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:n,name:s,keyframes:i,pseudoElement:r,allowFlatten:o=!1,finalKeyframe:a,onComplete:u}=e;this.isPseudoElement=!!r,this.allowFlatten=o,this.options=e,_e(typeof e.type!="string");const c=li(e);this.animation=ui(n,s,i,c,r),c.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){const l=qe(i,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(l):ii(n,s,l),this.animation.cancel()}u?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const e=this.animation.effect?.getComputedTiming?.().duration||0;return R(Number(e))}get time(){return R(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=E(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:n}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&ri()?(this.animation.timeline=e,q):n(this)}}const hn={anticipate:Wt,backInOut:jt,circInOut:$t};function hi(t){return t in hn}function fi(t){typeof t.ease=="string"&&hi(t.ease)&&(t.ease=hn[t.ease])}const ft=10;class di extends ci{constructor(e){fi(e),rn(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:n,onUpdate:s,onComplete:i,element:r,...o}=this.options;if(!n)return;if(e!==void 0){n.set(e);return}const a=new Xe({...o,autoplay:!1}),u=E(this.finishedTime??this.time);n.setWithVelocity(a.sample(u-ft).value,a.sample(u).value,ft),a.stop()}}const dt=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Y.test(t)||t==="0")&&!t.startsWith("url("));function pi(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function mi(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const r=t[t.length-1],o=dt(i,e),a=dt(r,e);return!o||!a?!1:pi(t)||(n==="spring"||cn(n))&&s}function Oe(t){t.duration=0,t.type}const gi=new Set(["opacity","clipPath","filter","transform"]),yi=Ue(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function bi(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:r,type:o}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;const{onUpdate:u,transformTemplate:c}=e.owner.getProps();return yi()&&n&&gi.has(n)&&(n!=="transform"||!c)&&!u&&!s&&i!=="mirror"&&r!==0&&o!=="inertia"}const vi=40;class Ti extends Ye{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:r=0,repeatType:o="loop",keyframes:a,name:u,motionValue:c,element:l,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=I.now();const f={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:r,repeatType:o,name:u,motionValue:c,element:l,...h},d=l?.KeyframeResolver||Ze;this.keyframeResolver=new d(a,(v,T,b)=>this.onKeyframesResolved(v,T,f,!b),u,c,l),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,n,s,i){this.keyframeResolver=void 0;const{name:r,type:o,velocity:a,delay:u,isHandoff:c,onUpdate:l}=s;this.resolvedAt=I.now(),mi(e,r,o,a)||((K.instantAnimations||!u)&&l?.(qe(e,s,n)),e[0]=e[e.length-1],Oe(s),s.repeat=0);const f={startTime:i?this.resolvedAt?this.resolvedAt-this.createdAt>vi?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:e},d=!c&&bi(f)?new di({...f,element:f.motionValue.owner.current}):new Xe(f);d.finished.then(()=>this.notifyFinished()).catch(q),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,n){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),ni()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const Vi=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Ai(t){const e=Vi.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function fn(t,e,n=1){const[s,i]=Ai(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const o=r.trim();return Rt(o)?parseFloat(o):o}return Le(i)?fn(i,e,n+1):i}function dn(t,e){return t?.[e]??t?.default??t}const pn=new Set(["width","height","top","left","right","bottom",...Ct]),wi={test:t=>t==="auto",parse:t=>t},mn=t=>e=>e.test(t),gn=[Be,Mt,ve,En,Rn,Kn,wi],pt=t=>gn.find(mn(t));function Si(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Kt(t):!0}const xi=new Set(["brightness","contrast","saturate","opacity"]);function Mi(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(We)||[];if(!s)return t;const i=n.replace(s,"");let r=xi.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Ci=/\b([a-z-]*)\(.*?\)/gu,Ee={...Y,getAnimatableNone:t=>{const e=t.match(Ci);return e?e.map(Mi).join(" "):t}},Pi={...Nn,color:x,backgroundColor:x,outlineColor:x,fill:x,stroke:x,borderColor:x,borderTopColor:x,borderRightColor:x,borderBottomColor:x,borderLeftColor:x,filter:Ee,WebkitFilter:Ee},yn=t=>Pi[t];function bn(t,e){let n=yn(t);return n!==Ee&&(n=Y),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Fi=new Set(["auto","none","0"]);function Di(t,e,n){let s=0,i;for(;s<t.length&&!i;){const r=t[s];typeof r=="string"&&!Fi.has(r)&&ee(r).values.length&&(i=t[s]),s++}if(i&&n)for(const r of e)t[r]=bn(n,i)}class Ii extends Ze{constructor(e,n,s,i,r){super(e,n,s,i,r,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let u=0;u<e.length;u++){let c=e[u];if(typeof c=="string"&&(c=c.trim(),Le(c))){const l=fn(c,n.current);l!==void 0&&(e[u]=l),u===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!pn.has(s)||e.length!==2)return;const[i,r]=e,o=pt(i),a=pt(r);if(o!==a)if(ct(o)&&ct(a))for(let u=0;u<e.length;u++){const c=e[u];typeof c=="string"&&(e[u]=parseFloat(c))}else U[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)(e[i]===null||Si(e[i]))&&s.push(i);s.length&&Di(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=U[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){const{element:e,name:n,unresolvedKeyframes:s}=this;if(!e||!e.current)return;const i=e.getValue(n);i&&i.jump(this.measuredOrigin,!1);const r=s.length-1,o=s[r];s[r]=U[n](e.measureViewportBox(),window.getComputedStyle(e.current)),o!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([a,u])=>{e.getValue(a).set(u)}),this.resolveNoneKeyframes()}}function Oi(t,e,n){if(t instanceof EventTarget)return[t];if(typeof t=="string"){let s=document;const i=n?.[t]??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}const mt=30,Ei=t=>!isNaN(parseFloat(t));class Ri{constructor(e,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=s=>{const i=I.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const r of this.dependents)r.dirty()},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=I.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=Ei(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new kt);const s=this.events[e].add(n);return e==="change"?()=>{s(),O.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e){this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=I.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>mt)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,mt);return Bt(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function le(t,e){return new Ri(t,e)}const{schedule:Ki}=qt(queueMicrotask,!1),Ni={y:!1};function ki(){return Ni.y}function vn(t,e){const n=Oi(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}function gt(t){return!(t.pointerType==="touch"||ki())}function Bi(t,e,n={}){const[s,i,r]=vn(t,n),o=a=>{if(!gt(a))return;const{target:u}=a,c=e(u,a);if(typeof c!="function"||!u)return;const l=h=>{gt(h)&&(c(h),u.removeEventListener("pointerleave",l))};u.addEventListener("pointerleave",l,i)};return s.forEach(a=>{a.addEventListener("pointerenter",o,i)}),r}const Tn=(t,e)=>e?t===e?!0:Tn(t,e.parentElement):!1,Li=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,_i=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Ui(t){return _i.has(t.tagName)||t.tabIndex!==-1}const ie=new WeakSet;function yt(t){return e=>{e.key==="Enter"&&t(e)}}function ye(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const Gi=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=yt(()=>{if(ie.has(n))return;ye(n,"down");const i=yt(()=>{ye(n,"up")}),r=()=>ye(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",r,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function bt(t){return Li(t)&&!0}function ji(t,e,n={}){const[s,i,r]=vn(t,n),o=a=>{const u=a.currentTarget;if(!bt(a))return;ie.add(u);const c=e(u,a),l=(d,v)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),ie.has(u)&&ie.delete(u),bt(d)&&typeof c=="function"&&c(d,{success:v})},h=d=>{l(d,u===window||u===document||n.useGlobalTarget||Tn(u,d.target))},f=d=>{l(d,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",f,i)};return s.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",o,i),kn(a)&&(a.addEventListener("focus",c=>Gi(c,i)),!Ui(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),r}const Wi=[...gn,x,Y],$i=t=>Wi.find(mn(t));function Hi({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function zi(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function qi(t,e){return Hi(zi(t.getBoundingClientRect(),e))}const vt=()=>({min:0,max:0}),Vn=()=>({x:vt(),y:vt()}),Re={current:null},An={current:!1};function Yi(){if(An.current=!0,!!Bn)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Re.current=t.matches;t.addEventListener("change",e),e()}else Re.current=!1}const Xi=new WeakMap;function Zi(t,e,n){for(const s in e){const i=e[s],r=n[s];if(N(i))t.addValue(s,i);else if(N(r))t.addValue(s,le(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const o=t.getValue(s);o.liveStyle===!0?o.jump(i):o.hasAnimated||o.set(i)}else{const o=t.getStaticValue(s);t.addValue(s,le(o!==void 0?o:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const Tt=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Ji{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:r,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ze,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=I.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,O.render(this.render,!1,!0))};const{latestValues:u,renderState:c}=o;this.latestValues=u,this.baseTarget={...u},this.initialValues=n.initial?{...u}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=Ln(n),this.isVariantNode=_n(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:l,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in h){const d=h[f];u[f]!==void 0&&N(d)&&d.set(u[f])}}mount(e){this.current=e,Xi.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),An.current||Yi(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Re.current,this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Te(this.notifyUpdate),Te(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}addChild(e){this.children.add(e),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(e)}removeChild(e){this.children.delete(e),this.enteringChildren&&this.enteringChildren.delete(e)}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=ce.has(e);s&&this.onBindTransform&&this.onBindTransform();const i=n.on("change",o=>{this.latestValues[e]=o,this.props.onUpdate&&O.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),r&&r(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in tt){const n=tt[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const r=this.features[e];r.isMounted?r.update():(r.mount(),r.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Vn()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<Tt.length;s++){const i=Tt[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const r="on"+i,o=e[r];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=Zi(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=le(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){let s=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return s!=null&&(typeof s=="string"&&(Rt(s)||Kt(s))?s=parseFloat(s):!$i(s)&&Y.test(n)&&(s=bn(e,n)),this.setBaseTarget(e,N(s)?s.get():s)),N(s)?s.get():s}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){const{initial:n}=this.props;let s;if(typeof n=="string"||typeof n=="object"){const r=Pt(this.props,n,this.presenceContext?.custom);r&&(s=r[e])}if(n&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,e);return i!==void 0&&!N(i)?i:this.initialValues[e]!==void 0&&s===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new kt),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}scheduleRenderMicrotask(){Ki.render(this.render)}}class wn extends Ji{constructor(){super(...arguments),this.KeyframeResolver=Ii}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;N(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Sn(t,{style:e,vars:n},s,i){const r=t.style;let o;for(o in e)r[o]=e[o];i?.applyProjectionStyles(r,s);for(o in n)r.setProperty(o,n[o])}function Qi(t){return window.getComputedStyle(t)}class er extends wn{constructor(){super(...arguments),this.type="html",this.renderInstance=Sn}readValueFromInstance(e,n){if(ce.has(n))return this.projection?.isProjecting?Ce(n):Zs(e,n);{const s=Qi(e),i=(Un(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return qi(e,n)}build(e,n,s){Gn(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return jn(e,n,s)}}const xn=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function tr(t,e,n,s){Sn(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(xn.has(i)?i:Ft(i),e.attrs[i])}class nr extends wn{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Vn}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(ce.has(n)){const s=yn(n);return s&&s.default||0}return n=xn.has(n)?n:Ft(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return Wn(e,n,s)}build(e,n,s){$n(e,n,this.isSVGTag,s.transformTemplate,s.style)}renderInstance(e,n,s,i){tr(e,n,s,i)}mount(e){this.isSVGTag=Hn(e.tagName),super.mount(e)}}const sr=(t,e)=>zn(t)?new nr(e):new er(e,{allowProjection:t!==qn.Fragment});function H(t,e,n){const s=t.getProps();return Pt(s,e,n!==void 0?n:s.custom,t)}const Ke=t=>Array.isArray(t);function ir(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,le(n))}function rr(t){return Ke(t)?t[t.length-1]||0:t}function or(t,e){const n=H(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const o in r){const a=rr(r[o]);ir(t,o,a)}}function ar(t){return!!(N(t)&&t.add)}function ur(t,e){const n=t.getValue("willChange");if(ar(n))return n.add(e);if(!n&&K.WillChange){const s=new K.WillChange("auto");t.addValue("willChange",s),s.add(e)}}function lr(t){return t.props[Yn]}const cr=t=>t!==null;function hr(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(cr),r=e&&n!=="loop"&&e%2===1?0:i.length-1;return i[r]}const fr={type:"spring",stiffness:500,damping:25,restSpeed:10},dr=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),pr={type:"keyframes",duration:.8},mr={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},gr=(t,{keyframes:e})=>e.length>2?pr:ce.has(t)?t.startsWith("scale")?dr(e[1]):fr:mr;function yr({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:u,elapsed:c,...l}){return!!Object.keys(l).length}const br=(t,e,n,s={},i,r)=>o=>{const a=dn(s,t)||{},u=a.delay||s.delay||0;let{elapsed:c=0}=s;c=c-E(u);const l={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:f=>{e.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:i};yr(a)||Object.assign(l,gr(t,l)),l.duration&&(l.duration=E(l.duration)),l.repeatDelay&&(l.repeatDelay=E(l.repeatDelay)),l.from!==void 0&&(l.keyframes[0]=l.from);let h=!1;if((l.type===!1||l.duration===0&&!l.repeatDelay)&&(Oe(l),l.delay===0&&(h=!0)),(K.instantAnimations||K.skipAnimations)&&(h=!0,Oe(l),l.delay=0),l.allowFlatten=!a.type&&!a.ease,h&&!r&&e.get()!==void 0){const f=hr(l.keyframes,a);if(f!==void 0){O.update(()=>{l.onUpdate(f),l.onComplete()});return}}return a.isSync?new Xe(l):new Ti(l)};function vr({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function Mn(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...a}=e;s&&(r=s);const u=[],c=i&&t.animationState&&t.animationState.getState()[i];for(const l in a){const h=t.getValue(l,t.latestValues[l]??null),f=a[l];if(f===void 0||c&&vr(c,l))continue;const d={delay:n,...dn(r||{},l)},v=h.get();if(v!==void 0&&!h.isAnimating&&!Array.isArray(f)&&f===v&&!d.velocity)continue;let T=!1;if(window.MotionHandoffAnimation){const p=lr(t);if(p){const V=window.MotionHandoffAnimation(p,l,O);V!==null&&(d.startTime=V,T=!0)}}ur(t,l),h.start(br(l,h,f,t.shouldReduceMotion&&pn.has(l)?{type:!1}:d,t,T));const b=h.animation;b&&u.push(b)}return o&&Promise.all(u).then(()=>{O.update(()=>{o&&or(t,o)})}),u}function Cn(t,e,n,s=0,i=1){const r=Array.from(t).sort((c,l)=>c.sortNodePosition(l)).indexOf(e),o=t.size,a=(o-1)*s;return typeof n=="function"?n(r,o):i===1?r*s:a-r*s}function Ne(t,e,n={}){const s=H(t,e,n.type==="exit"?t.presenceContext?.custom:void 0);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const r=s?()=>Promise.all(Mn(t,s,n)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:l,staggerDirection:h}=i;return Tr(t,e,u,c,l,h,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[u,c]=a==="beforeChildren"?[r,o]:[o,r];return u().then(()=>c())}else return Promise.all([r(),o(n.delay)])}function Tr(t,e,n=0,s=0,i=0,r=1,o){const a=[];for(const u of t.variantChildren)u.notify("AnimationStart",e),a.push(Ne(u,e,{...o,delay:n+(typeof s=="function"?0:s)+Cn(t.variantChildren,u,s,i,r)}).then(()=>u.notify("AnimationComplete",e)));return Promise.all(a)}function Vr(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(r=>Ne(t,r,n));s=Promise.all(i)}else if(typeof e=="string")s=Ne(t,e,n);else{const i=typeof e=="function"?H(t,e,n.custom):e;s=Promise.all(Mn(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}function Pn(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const Ar=It.length;function Fn(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?Fn(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<Ar;n++){const s=It[n],i=t.props[s];(Dt(i)||i===!1)&&(e[s]=i)}return e}const wr=[...Et].reverse(),Sr=Et.length;function xr(t){return e=>Promise.all(e.map(({animation:n,options:s})=>Vr(t,n,s)))}function Mr(t){let e=xr(t),n=Vt(),s=!0;const i=u=>(c,l)=>{const h=H(t,l,u==="exit"?t.presenceContext?.custom:void 0);if(h){const{transition:f,transitionEnd:d,...v}=h;c={...c,...v,...d}}return c};function r(u){e=u(t)}function o(u){const{props:c}=t,l=Fn(t.parent)||{},h=[],f=new Set;let d={},v=1/0;for(let b=0;b<Sr;b++){const p=wr[b],V=n[p],g=c[p]!==void 0?c[p]:l[p],w=Dt(g),m=p===u?V.isActive:null;m===!1&&(v=b);let A=g===l[p]&&g!==c[p]&&w;if(A&&s&&t.manuallyAnimateOnMount&&(A=!1),V.protectedKeys={...d},!V.isActive&&m===null||!g&&!V.prevProp||Ot(g)||typeof g=="boolean")continue;const M=Cr(V.prevProp,g);let y=M||p===u&&V.isActive&&!A&&w||b>v&&w,P=!1;const F=Array.isArray(g)?g:[g];let j=F.reduce(i(p),{});m===!1&&(j={});const{prevResolvedValues:Je={}}=V,In={...Je,...j},Qe=C=>{y=!0,f.has(C)&&(P=!0,f.delete(C)),V.needsAnimating[C]=!0;const D=t.getValue(C);D&&(D.liveStyle=!1)};for(const C in In){const D=j[C],k=Je[C];if(d.hasOwnProperty(C))continue;let W=!1;Ke(D)&&Ke(k)?W=!Pn(D,k):W=D!==k,W?D!=null?Qe(C):f.add(C):D!==void 0&&f.has(C)?Qe(C):V.protectedKeys[C]=!0}V.prevProp=g,V.prevResolvedValues=j,V.isActive&&(d={...d,...j}),s&&t.blockInitialAnimation&&(y=!1);const et=A&&M;y&&(!et||P)&&h.push(...F.map(C=>{const D={type:p};if(typeof C=="string"&&s&&!et&&t.manuallyAnimateOnMount&&t.parent){const{parent:k}=t,W=H(k,C);if(k.enteringChildren&&W){const{delayChildren:On}=W.transition||{};D.delay=Cn(k.enteringChildren,t,On)}}return{animation:C,options:D}}))}if(f.size){const b={};if(typeof c.initial!="boolean"){const p=H(t,Array.isArray(c.initial)?c.initial[0]:c.initial);p&&p.transition&&(b.transition=p.transition)}f.forEach(p=>{const V=t.getBaseTarget(p),g=t.getValue(p);g&&(g.liveStyle=!0),b[p]=V??null}),h.push({animation:b})}let T=!!h.length;return s&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(T=!1),s=!1,T?e(h):Promise.resolve()}function a(u,c){if(n[u].isActive===c)return Promise.resolve();t.variantChildren?.forEach(h=>h.animationState?.setActive(u,c)),n[u].isActive=c;const l=o(u);for(const h in n)n[h].protectedKeys={};return l}return{animateChanges:o,setActive:a,setAnimateFunction:r,getState:()=>n,reset:()=>{n=Vt(),s=!0}}}function Cr(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Pn(e,t):!1}function B(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Vt(){return{animate:B(!0),whileInView:B(),whileHover:B(),whileTap:B(),whileDrag:B(),whileFocus:B(),exit:B()}}class X{constructor(e){this.isMounted=!1,this.node=e}update(){}}class Pr extends X{constructor(e){super(e),e.animationState||(e.animationState=Mr(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Ot(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let Fr=0;class Dr extends X{constructor(){super(...arguments),this.id=Fr++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>{n(this.id)})}mount(){const{register:e,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const Ir={animation:{Feature:Pr},exit:{Feature:Dr}};function At(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function Dn(t){return{point:{x:t.pageX,y:t.pageY}}}function wt(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,r=s[i];r&&O.postRender(()=>r(e,Dn(e)))}class Or extends X{mount(){const{current:e}=this.node;e&&(this.unmount=Bi(e,(n,s)=>(wt(this.node,s,"Start"),i=>wt(this.node,i,"End"))))}unmount(){}}class Er extends X{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=he(At(this.node.current,"focus",()=>this.onFocus()),At(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function St(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),r=s[i];r&&O.postRender(()=>r(e,Dn(e)))}class Rr extends X{mount(){const{current:e}=this.node;e&&(this.unmount=ji(e,(n,s)=>(St(this.node,s,"Start"),(i,{success:r})=>St(this.node,i,r?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const ke=new WeakMap,be=new WeakMap,Kr=t=>{const e=ke.get(t.target);e&&e(t)},Nr=t=>{t.forEach(Kr)};function kr({root:t,...e}){const n=t||document;be.has(n)||be.set(n,{});const s=be.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Nr,{root:t,...e})),s[i]}function Br(t,e,n){const s=kr(e);return ke.set(t,n),s.observe(t),()=>{ke.delete(t),s.unobserve(t)}}const Lr={some:0,all:1};class _r extends X{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:r}=e,o={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:Lr[i]},a=u=>{const{isIntersecting:c}=u;if(this.isInView===c||(this.isInView=c,r&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:l,onViewportLeave:h}=this.node.getProps(),f=c?l:h;f&&f(u)};return Br(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Ur(e,n))&&this.startObserver()}unmount(){}}function Ur({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const Gr={inView:{Feature:_r},tap:{Feature:Rr},focus:{Feature:Er},hover:{Feature:Or}},jr={renderer:sr,...Ir,...Gr};var Hr=jr;export{Hr as default};
