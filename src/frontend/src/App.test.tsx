/**
 * App组件测试
 * 
 * 测试主应用组件的基本功能
 */

import { describe, it, expect } from 'vitest'
import { renderWithProvidersNoRouter } from './test/utils'
import App from './App'

describe('App Component', () => {
  it('应该正常渲染', () => {
    // Given: App组件
    // When: 渲染组件
    renderWithProvidersNoRouter(<App />)

    // Then: 组件应该正常显示
    expect(document.body).toBeInTheDocument()
  })

  it('应该包含主要的应用结构', () => {
    // Given: App组件
    // When: 渲染组件
    const { container } = renderWithProvidersNoRouter(<App />)

    // Then: 应该包含基本的应用元素
    // 检查容器是否存在并包含内容
    expect(container).toBeInTheDocument()
    expect(container.firstChild).toBeInTheDocument()
  })
})
