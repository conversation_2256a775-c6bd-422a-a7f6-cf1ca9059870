/**
 * 测试工具函数
 * 
 * 提供常用的测试辅助函数和组件包装器
 */

import React, { type ReactElement } from 'react'
import { render, type RenderOptions } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { HeroUIProvider } from '@heroui/react'

// 自定义渲染函数的Props类型
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[]
  route?: string
}

// 创建测试包装器组件（包含Router）
const AllTheProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <BrowserRouter>
      <HeroUIProvider>
        {children}
      </HeroUIProvider>
    </BrowserRouter>
  )
}

// 创建测试包装器组件（不包含Router，用于测试已经包含Router的组件）
const ProvidersWithoutRouter: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <HeroUIProvider>
      {children}
    </HeroUIProvider>
  )
}

/**
 * 自定义渲染函数
 * 包装了所有必要的Provider
 */
export const renderWithProviders = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { initialEntries, route, ...renderOptions } = options

  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <AllTheProviders>{children}</AllTheProviders>
  )

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

/**
 * 自定义渲染函数（不包含Router）
 * 用于测试已经包含Router的组件，如App组件
 */
export const renderWithProvidersNoRouter = (
  ui: ReactElement,
  options: Omit<RenderOptions, 'wrapper'> = {}
) => {
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <ProvidersWithoutRouter>{children}</ProvidersWithoutRouter>
  )

  return render(ui, { wrapper: Wrapper, ...options })
}

/**
 * 创建模拟的API响应
 */
export const createMockApiResponse = <T,>(data: T, status = 200) => ({
  data,
  status,
  statusText: 'OK',
  headers: {},
  config: {},
})

/**
 * 创建模拟的错误响应
 */
export const createMockErrorResponse = (message: string, status = 400) => ({
  response: {
    data: { message },
    status,
    statusText: 'Bad Request',
  },
})

/**
 * 等待异步操作完成
 */
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0))

/**
 * 模拟用户数据
 */
export const mockUser = {
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  fullName: 'Test User',
  isActive: true,
}

/**
 * 模拟渠道数据
 */
export const mockChannel = {
  id: '1',
  platform: 'wechat',
  channelId: 'wxid_test123',
  channelName: '测试微信号',
  alias: '测试账号',
  status: 'connected',
  config: {
    autoReply: true,
    aiEnabled: true,
  },
}

// 重新导出testing-library的所有内容
export * from '@testing-library/react'
export { default as userEvent } from '@testing-library/user-event'
