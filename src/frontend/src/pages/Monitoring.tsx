import { useEffect, useState } from 'react'
import {
  Card,
  CardHeader,
  CardBody,
  Button,
  Progress,
  Chip
} from "@heroui/react"
import { apiClient } from '../services/api'

interface SystemMetrics {
  cpu: {
    usage_percent: number
    count: number
    frequency?: number
  }
  memory: {
    total: number
    available: number
    used: number
    usage_percent: number
  }
  disk: {
    total: number
    used: number
    free: number
    usage_percent: number
  }
  uptime: number
}

export function Monitoring() {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())

  const fetchMetrics = async () => {
    try {
      const response = await apiClient.get('/api/v1/system/metrics')
      setMetrics(response.data)
      setLastUpdate(new Date())
    } catch (error) {
      console.error('获取系统指标失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMetrics()
    
    // 每10秒刷新一次
    const interval = setInterval(fetchMetrics, 10000)
    return () => clearInterval(interval)
  }, [])

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (days > 0) {
      return `${days}天 ${hours}小时 ${minutes}分钟`
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`
    } else {
      return `${minutes}分钟`
    }
  }

  const getUsageColor = (percentage: number) => {
    if (percentage < 50) return 'success'
    if (percentage < 80) return 'warning'
    return 'danger'
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Progress
          size="sm"
          isIndeterminate
          aria-label="加载中..."
          className="max-w-md"
        />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">系统监控</h1>
        <div className="flex gap-2 items-center">
          <span className="text-sm text-default-500">
            最后更新: {lastUpdate.toLocaleTimeString()}
          </span>
          <Button 
            color="primary" 
            variant="flat"
            size="sm"
            onPress={fetchMetrics}
          >
            刷新
          </Button>
        </div>
      </div>

      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* CPU 使用率 */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center w-full">
                <h4 className="font-bold text-large">CPU 使用率</h4>
                <Chip 
                  color={getUsageColor(metrics.cpu.usage_percent)} 
                  variant="flat"
                  size="sm"
                >
                  {metrics?.cpu?.usage_percent?.toFixed(1) || '0'}%
                </Chip>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <Progress
                value={metrics.cpu.usage_percent}
                color={getUsageColor(metrics.cpu.usage_percent)}
                className="mb-4"
              />
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>核心数</span>
                  <span>{metrics.cpu.count}</span>
                </div>
                {metrics.cpu.frequency && (
                  <div className="flex justify-between">
                    <span>频率</span>
                    <span>{((metrics?.cpu?.frequency || 0) / 1000).toFixed(2)} GHz</span>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>

          {/* 内存使用率 */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center w-full">
                <h4 className="font-bold text-large">内存使用率</h4>
                <Chip 
                  color={getUsageColor(metrics.memory.usage_percent)} 
                  variant="flat"
                  size="sm"
                >
                  {metrics?.memory?.usage_percent?.toFixed(1) || '0'}%
                </Chip>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <Progress
                value={metrics.memory.usage_percent}
                color={getUsageColor(metrics.memory.usage_percent)}
                className="mb-4"
              />
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>已使用</span>
                  <span>{formatBytes(metrics.memory.used)}</span>
                </div>
                <div className="flex justify-between">
                  <span>可用</span>
                  <span>{formatBytes(metrics.memory.available)}</span>
                </div>
                <div className="flex justify-between">
                  <span>总计</span>
                  <span>{formatBytes(metrics.memory.total)}</span>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* 磁盘使用率 */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center w-full">
                <h4 className="font-bold text-large">磁盘使用率</h4>
                <Chip 
                  color={getUsageColor(metrics.disk.usage_percent)} 
                  variant="flat"
                  size="sm"
                >
                  {metrics?.disk?.usage_percent?.toFixed(1) || '0'}%
                </Chip>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <Progress
                value={metrics.disk.usage_percent}
                color={getUsageColor(metrics.disk.usage_percent)}
                className="mb-4"
              />
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>已使用</span>
                  <span>{formatBytes(metrics.disk.used)}</span>
                </div>
                <div className="flex justify-between">
                  <span>可用</span>
                  <span>{formatBytes(metrics.disk.free)}</span>
                </div>
                <div className="flex justify-between">
                  <span>总计</span>
                  <span>{formatBytes(metrics.disk.total)}</span>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* 系统运行时间 */}
      {metrics && (
        <Card>
          <CardHeader>
            <h4 className="font-bold text-large">系统信息</h4>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-default-500 mb-1">系统运行时间</div>
                <div className="text-xl font-semibold text-success">
                  {formatUptime(metrics.uptime)}
                </div>
              </div>
              <div>
                <div className="text-sm text-default-500 mb-1">监控状态</div>
                <Chip color="success" variant="flat">
                  正常运行
                </Chip>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* 性能建议 */}
      {metrics && (
        <Card>
          <CardHeader>
            <h4 className="font-bold text-large">性能建议</h4>
          </CardHeader>
          <CardBody>
            <div className="space-y-3">
              {metrics.cpu.usage_percent > 80 && (
                <div className="p-3 bg-danger-50 rounded-lg border border-danger-200">
                  <div className="text-danger font-medium">⚠️ CPU使用率过高</div>
                  <div className="text-sm text-danger-600 mt-1">
                    建议检查是否有异常进程占用过多CPU资源
                  </div>
                </div>
              )}
              
              {metrics.memory.usage_percent > 80 && (
                <div className="p-3 bg-danger-50 rounded-lg border border-danger-200">
                  <div className="text-danger font-medium">⚠️ 内存使用率过高</div>
                  <div className="text-sm text-danger-600 mt-1">
                    建议释放不必要的内存或增加系统内存
                  </div>
                </div>
              )}
              
              {metrics.disk.usage_percent > 80 && (
                <div className="p-3 bg-warning-50 rounded-lg border border-warning-200">
                  <div className="text-warning font-medium">⚠️ 磁盘空间不足</div>
                  <div className="text-sm text-warning-600 mt-1">
                    建议清理不必要的文件或扩展磁盘空间
                  </div>
                </div>
              )}
              
              {metrics.cpu.usage_percent < 50 && metrics.memory.usage_percent < 50 && metrics.disk.usage_percent < 50 && (
                <div className="p-3 bg-success-50 rounded-lg border border-success-200">
                  <div className="text-success font-medium">✅ 系统运行良好</div>
                  <div className="text-sm text-success-600 mt-1">
                    所有系统指标都在正常范围内
                  </div>
                </div>
              )}
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  )
}
