import { useState } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>oot<PERSON>,
  <PERSON><PERSON>,
  Input,
  Switch,
  Select,
  SelectItem,
  Chip
} from "@heroui/react"

interface Settings {
  general: {
    appName: string
    language: string
    timezone: string
    autoSave: boolean
  }
  ai: {
    provider: string
    apiKey: string
    model: string
    temperature: number
    maxTokens: number
    enableAutoReply: boolean
  }
  notifications: {
    emailNotifications: boolean
    pushNotifications: boolean
    soundAlerts: boolean
  }
}

const languageOptions = [
  { key: 'zh-CN', label: '简体中文' },
  { key: 'en-US', label: 'English' },
  { key: 'ja-JP', label: '日本語' }
]

const timezoneOptions = [
  { key: 'Asia/Shanghai', label: '北京时间 (UTC+8)' },
  { key: 'America/New_York', label: '纽约时间 (UTC-5)' },
  { key: 'Europe/London', label: '伦敦时间 (UTC+0)' }
]

const aiProviderOptions = [
  { key: 'openai', label: 'OpenAI' },
  { key: 'anthropic', label: 'Anthropic' },
  { key: 'azure', label: 'Azure OpenAI' },
  { key: 'local', label: '本地模型' }
]

export function Settings() {
  const [settings, setSettings] = useState<Settings>({
    general: {
      appName: '柴管家',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      autoSave: true
    },
    ai: {
      provider: 'openai',
      apiKey: '',
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 1000,
      enableAutoReply: false
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: false,
      soundAlerts: true
    }
  })

  const [saved, setSaved] = useState(false)

  const handleSave = () => {
    // 这里应该调用API保存设置
    console.log('保存设置:', settings)
    setSaved(true)
    setTimeout(() => setSaved(false), 3000)
  }

  const updateGeneralSettings = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      general: { ...prev.general, [key]: value }
    }))
  }

  const updateAISettings = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      ai: { ...prev.ai, [key]: value }
    }))
  }

  const updateNotificationSettings = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      notifications: { ...prev.notifications, [key]: value }
    }))
  }

  return (
    <div className="space-y-8">
      {/* 页面头部 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            系统设置
          </h1>
          <p className="text-default-500 mt-1">配置系统参数和个人偏好</p>
        </div>
        <div className="flex gap-3 items-center">
          {saved && (
            <Chip
              color="success"
              variant="flat"
              size="sm"
              startContent={<span>✅</span>}
              className="animate-in fade-in duration-300"
            >
              设置已保存
            </Chip>
          )}
          <Button
            color="primary"
            onPress={handleSave}
            startContent={<span>💾</span>}
            className="font-medium"
          >
            保存设置
          </Button>
        </div>
      </div>

      {/* 设置分组 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 基础设置 */}
        <Card className="border-none shadow-lg bg-gradient-to-br from-default-50 to-default-100">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">⚙️</span>
              </div>
              <div>
                <h4 className="font-bold text-large text-primary-700">基础设置</h4>
                <p className="text-xs text-primary-600">应用基本配置</p>
              </div>
            </div>
          </CardHeader>
          <CardBody className="space-y-6">
            <div className="space-y-2">
              <Input
                label="应用名称"
                value={settings.general.appName}
                onValueChange={(value) => updateGeneralSettings('appName', value)}
                startContent={<span>🏷️</span>}
                className="font-medium"
              />
            </div>

            <div className="space-y-2">
              <Select
                label="语言"
                selectedKeys={[settings.general.language]}
                onSelectionChange={(keys) => {
                  const language = Array.from(keys)[0] as string
                  updateGeneralSettings('language', language)
                }}
                startContent={<span>🌐</span>}
              >
                {languageOptions.map((option) => (
                  <SelectItem key={option.key}>
                    {option.label}
                  </SelectItem>
                ))}
              </Select>
            </div>

            <div className="space-y-2">
              <Select
                label="时区"
                selectedKeys={[settings.general.timezone]}
                onSelectionChange={(keys) => {
                  const timezone = Array.from(keys)[0] as string
                  updateGeneralSettings('timezone', timezone)
                }}
                startContent={<span>🕒</span>}
              >
                {timezoneOptions.map((option) => (
                  <SelectItem key={option.key}>
                    {option.label}
                  </SelectItem>
                ))}
              </Select>
            </div>

            <div className="flex justify-between items-center p-4 bg-white/50 rounded-lg">
              <div className="flex items-center gap-3">
                <span className="text-lg">💾</span>
                <div>
                  <div className="font-medium">自动保存</div>
                  <div className="text-sm text-default-500">自动保存配置更改</div>
                </div>
              </div>
              <Switch
                isSelected={settings.general.autoSave}
                onValueChange={(value) => updateGeneralSettings('autoSave', value)}
                color="primary"
              />
            </div>
          </CardBody>
        </Card>

        {/* 通知设置 */}
        <Card className="border-none shadow-lg bg-gradient-to-br from-secondary-50 to-secondary-100">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-secondary-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">🔔</span>
              </div>
              <div>
                <h4 className="font-bold text-large text-secondary-700">通知设置</h4>
                <p className="text-xs text-secondary-600">消息提醒配置</p>
              </div>
            </div>
          </CardHeader>
          <CardBody className="space-y-6">
            <div className="flex justify-between items-center p-4 bg-white/50 rounded-lg">
              <div className="flex items-center gap-3">
                <span className="text-lg">📧</span>
                <div>
                  <div className="font-medium">邮件通知</div>
                  <div className="text-sm text-default-500">接收重要事件的邮件通知</div>
                </div>
              </div>
              <Switch
                isSelected={settings.notifications.emailNotifications}
                onValueChange={(value) => updateNotificationSettings('emailNotifications', value)}
                color="secondary"
              />
            </div>

            <div className="flex justify-between items-center p-4 bg-white/50 rounded-lg">
              <div className="flex items-center gap-3">
                <span className="text-lg">📱</span>
                <div>
                  <div className="font-medium">推送通知</div>
                  <div className="text-sm text-default-500">接收浏览器推送通知</div>
                </div>
              </div>
              <Switch
                isSelected={settings.notifications.pushNotifications}
                onValueChange={(value) => updateNotificationSettings('pushNotifications', value)}
                color="secondary"
              />
            </div>

            <div className="flex justify-between items-center p-4 bg-white/50 rounded-lg">
              <div className="flex items-center gap-3">
                <span className="text-lg">🔊</span>
                <div>
                  <div className="font-medium">声音提醒</div>
                  <div className="text-sm text-default-500">新消息时播放提示音</div>
                </div>
              </div>
              <Switch
                isSelected={settings.notifications.soundAlerts}
                onValueChange={(value) => updateNotificationSettings('soundAlerts', value)}
                color="secondary"
              />
            </div>
          </CardBody>
        </Card>
      </div>

      {/* AI设置 - 独立的大卡片 */}
      <Card className="border-none shadow-lg bg-gradient-to-br from-warning-50 to-warning-100">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-warning-500 rounded-xl flex items-center justify-center">
              <span className="text-white text-xl">🤖</span>
            </div>
            <div>
              <h4 className="font-bold text-xl text-warning-700">AI设置</h4>
              <p className="text-sm text-warning-600">配置人工智能服务参数</p>
            </div>
          </div>
        </CardHeader>
        <CardBody className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <Select
                label="AI服务提供商"
                selectedKeys={[settings.ai.provider]}
                onSelectionChange={(keys) => {
                  const provider = Array.from(keys)[0] as string
                  updateAISettings('provider', provider)
                }}
                startContent={<span>🏢</span>}
                className="font-medium"
              >
                {aiProviderOptions.map((option) => (
                  <SelectItem key={option.key}>
                    {option.label}
                  </SelectItem>
                ))}
              </Select>

              <Input
                label="API密钥"
                type="password"
                placeholder="请输入API密钥"
                value={settings.ai.apiKey}
                onValueChange={(value) => updateAISettings('apiKey', value)}
                startContent={<span>🔑</span>}
                className="font-medium"
              />

              <Input
                label="模型名称"
                value={settings.ai.model}
                onValueChange={(value) => updateAISettings('model', value)}
                startContent={<span>🧠</span>}
                className="font-medium"
              />
            </div>

            <div className="space-y-4">
              <Input
                label="温度参数"
                type="number"
                min={0}
                max={2}
                step={0.1}
                value={settings.ai.temperature.toString()}
                onValueChange={(value) => updateAISettings('temperature', parseFloat(value))}
                description="控制AI回复的随机性，0-2之间"
                startContent={<span>🌡️</span>}
                className="font-medium"
              />

              <Input
                label="最大令牌数"
                type="number"
                min={1}
                max={4000}
                value={settings.ai.maxTokens.toString()}
                onValueChange={(value) => updateAISettings('maxTokens', parseInt(value))}
                description="限制AI回复的最大长度"
                startContent={<span>📏</span>}
                className="font-medium"
              />

              <div className="flex justify-between items-center p-4 bg-white/50 rounded-lg">
                <div className="flex items-center gap-3">
                  <span className="text-lg">🚀</span>
                  <div>
                    <div className="font-medium">启用自动回复</div>
                    <div className="text-sm text-default-500">AI自动回复用户消息</div>
                  </div>
                </div>
                <Switch
                  isSelected={settings.ai.enableAutoReply}
                  onValueChange={(value) => updateAISettings('enableAutoReply', value)}
                  color="warning"
                />
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* 系统信息 */}
      <Card className="border-none shadow-lg bg-gradient-to-br from-success-50 to-success-100">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-success-500 rounded-lg flex items-center justify-center">
              <span className="text-white text-lg">ℹ️</span>
            </div>
            <div>
              <h4 className="font-bold text-large text-success-700">系统信息</h4>
              <p className="text-xs text-success-600">当前系统状态和版本信息</p>
            </div>
          </div>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="p-4 bg-white/50 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <span>🏷️</span>
                  <div className="text-sm text-success-600">版本</div>
                </div>
                <div className="font-bold text-success-800">v1.0.0</div>
              </div>
              <div className="p-4 bg-white/50 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <span>🔗</span>
                  <div className="text-sm text-success-600">API地址</div>
                </div>
                <div className="font-medium text-success-800 text-sm">http://localhost:8000</div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="p-4 bg-white/50 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <span>📅</span>
                  <div className="text-sm text-success-600">构建时间</div>
                </div>
                <div className="font-medium text-success-800">2025-01-15</div>
              </div>
              <div className="p-4 bg-white/50 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <span>🌍</span>
                  <div className="text-sm text-success-600">环境</div>
                </div>
                <Chip color="warning" variant="solid" size="sm" className="font-medium">
                  开发环境
                </Chip>
              </div>
            </div>
          </div>
        </CardBody>
        <CardFooter className="pt-4">
          <div className="flex gap-3">
            <Button
              color="success"
              variant="flat"
              as="a"
              href="http://localhost:8000/docs"
              target="_blank"
              startContent={<span>📚</span>}
              className="font-medium"
            >
              查看API文档
            </Button>
            <Button
              color="default"
              variant="flat"
              startContent={<span>📊</span>}
              className="font-medium"
            >
              系统诊断
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
