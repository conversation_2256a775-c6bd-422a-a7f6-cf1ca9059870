import { useEffect, useState } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON>eader,
  CardBody,
  CardFooter,
  <PERSON><PERSON>,
  <PERSON>
} from "@heroui/react"
import { apiClient } from '../services/api'

interface SystemInfo {
  app_name: string
  version: string
  environment: string
}

interface HealthStatus {
  status: string
  app_name: string
  version: string
  environment: string
  database: string
  timestamp: number
}

export function Dashboard() {
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null)
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [infoResponse, healthResponse] = await Promise.all([
          apiClient.get('/api/v1/system/info'),
          apiClient.get('/health')
        ])
        
        setSystemInfo(infoResponse.data)
        setHealthStatus(healthResponse.data)
      } catch (error) {
        console.error('获取系统信息失败:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
    
    // 每30秒刷新一次健康状态
    const interval = setInterval(fetchData, 30000)
    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'success'
      case 'unhealthy':
        return 'danger'
      default:
        return 'warning'
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 w-48 bg-default-200 rounded-lg animate-pulse" />
          <div className="h-10 w-24 bg-default-200 rounded-lg animate-pulse" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 w-32 bg-default-200 rounded" />
              </CardHeader>
              <CardBody>
                <div className="space-y-3">
                  <div className="h-4 w-full bg-default-200 rounded" />
                  <div className="h-4 w-3/4 bg-default-200 rounded" />
                  <div className="h-4 w-1/2 bg-default-200 rounded" />
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* 页面头部 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            系统仪表板
          </h1>
          <p className="text-default-500 mt-1">实时监控系统状态和性能指标</p>
        </div>
        <div className="flex gap-2">
          <Button
            color="primary"
            variant="flat"
            onPress={() => window.location.reload()}
            startContent={<span>🔄</span>}
            className="font-medium"
          >
            刷新数据
          </Button>
        </div>
      </div>

      {/* 状态卡片网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 系统状态卡片 */}
        <Card className="border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-success-50 to-success-100">
          <CardHeader className="pb-2 flex items-center gap-3">
            <div className="w-10 h-10 bg-success-500 rounded-lg flex items-center justify-center">
              <span className="text-white text-lg">💚</span>
            </div>
            <div>
              <h4 className="font-bold text-large text-success-700">系统状态</h4>
              <p className="text-xs text-success-600">实时健康监控</p>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            {healthStatus && (
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-white/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">🚀</span>
                    <span className="font-medium">应用状态</span>
                  </div>
                  <Chip
                    color={getStatusColor(healthStatus.status)}
                    variant="solid"
                    size="sm"
                    className="font-medium"
                  >
                    {healthStatus.status === 'healthy' ? '正常' : '异常'}
                  </Chip>
                </div>
                <div className="flex justify-between items-center p-3 bg-white/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">🗄️</span>
                    <span className="font-medium">数据库</span>
                  </div>
                  <Chip
                    color={getStatusColor(healthStatus.database)}
                    variant="solid"
                    size="sm"
                    className="font-medium"
                  >
                    {healthStatus.database === 'healthy' ? '正常' : '异常'}
                  </Chip>
                </div>
                <div className="text-xs text-success-600 text-center mt-4 p-2 bg-white/30 rounded-lg">
                  🕒 最后更新: {new Date(healthStatus.timestamp * 1000).toLocaleString('zh-CN', {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              </div>
            )}
          </CardBody>
        </Card>

        {/* 系统信息卡片 */}
        <Card className="border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-primary-50 to-primary-100">
          <CardHeader className="pb-2 flex items-center gap-3">
            <div className="w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center">
              <span className="text-white text-lg">ℹ️</span>
            </div>
            <div>
              <h4 className="font-bold text-large text-primary-700">系统信息</h4>
              <p className="text-xs text-primary-600">应用详细信息</p>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            {systemInfo && (
              <div className="space-y-4">
                <div className="p-3 bg-white/50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-primary-600">应用名称</span>
                    <span className="font-bold text-primary-800">{systemInfo.app_name}</span>
                  </div>
                </div>
                <div className="p-3 bg-white/50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-primary-600">版本</span>
                    <Chip color="primary" variant="flat" size="sm" className="font-medium">
                      v{systemInfo.version}
                    </Chip>
                  </div>
                </div>
                <div className="p-3 bg-white/50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-primary-600">环境</span>
                    <Chip
                      color={systemInfo.environment === 'production' ? 'success' : 'warning'}
                      variant="solid"
                      size="sm"
                      className="font-medium"
                    >
                      {systemInfo.environment === 'development' ? '开发环境' : systemInfo.environment}
                    </Chip>
                  </div>
                </div>
              </div>
            )}
          </CardBody>
        </Card>

        {/* 快速操作卡片 */}
        <Card className="border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-secondary-50 to-secondary-100">
          <CardHeader className="pb-2 flex items-center gap-3">
            <div className="w-10 h-10 bg-secondary-500 rounded-lg flex items-center justify-center">
              <span className="text-white text-lg">⚡</span>
            </div>
            <div>
              <h4 className="font-bold text-large text-secondary-700">快速操作</h4>
              <p className="text-xs text-secondary-600">常用功能入口</p>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-3">
              <Button
                color="primary"
                variant="flat"
                className="w-full justify-start font-medium hover:scale-105 transition-transform"
                as="a"
                href="/channels"
                startContent={<span>📱</span>}
              >
                管理渠道
              </Button>
              <Button
                color="secondary"
                variant="flat"
                className="w-full justify-start font-medium hover:scale-105 transition-transform"
                as="a"
                href="/monitoring"
                startContent={<span>📊</span>}
              >
                查看监控
              </Button>
              <Button
                color="default"
                variant="flat"
                className="w-full justify-start font-medium hover:scale-105 transition-transform"
                as="a"
                href="http://localhost:8000/docs"
                target="_blank"
                startContent={<span>📚</span>}
              >
                API文档
              </Button>
            </div>
          </CardBody>
        </Card>

        {/* 统计数据卡片 */}
        <Card className="border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-warning-50 to-warning-100">
          <CardHeader className="pb-2 flex items-center gap-3">
            <div className="w-10 h-10 bg-warning-500 rounded-lg flex items-center justify-center">
              <span className="text-white text-lg">📈</span>
            </div>
            <div>
              <h4 className="font-bold text-large text-warning-700">今日统计</h4>
              <p className="text-xs text-warning-600">实时数据概览</p>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-4">
              <div className="p-3 bg-white/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-warning-600">活跃渠道</span>
                  <span className="font-bold text-2xl text-warning-800">3</span>
                </div>
              </div>
              <div className="p-3 bg-white/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-warning-600">处理消息</span>
                  <span className="font-bold text-2xl text-warning-800">127</span>
                </div>
              </div>
              <div className="p-3 bg-white/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-warning-600">AI回复率</span>
                  <span className="font-bold text-2xl text-warning-800">85%</span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* 欢迎信息和功能介绍 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2 border-none shadow-lg bg-gradient-to-br from-default-50 to-default-100">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-xl">柴</span>
              </div>
              <div>
                <h4 className="font-bold text-xl text-default-800">欢迎使用柴管家</h4>
                <p className="text-sm text-default-600">智能客服聚合系统</p>
              </div>
            </div>
          </CardHeader>
          <CardBody>
            <p className="text-default-700 leading-relaxed mb-4">
              柴管家是一个多平台智能客服聚合系统，帮助您统一管理来自不同社交平台的消息，
              并通过AI技术提供智能回复功能。
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-6">
              <div className="flex items-center gap-3 p-3 bg-white/50 rounded-lg">
                <span className="text-2xl">🤖</span>
                <div>
                  <p className="font-medium text-sm">AI智能回复</p>
                  <p className="text-xs text-default-500">自动处理客户咨询</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-white/50 rounded-lg">
                <span className="text-2xl">🔗</span>
                <div>
                  <p className="font-medium text-sm">多平台聚合</p>
                  <p className="text-xs text-default-500">统一管理所有渠道</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-white/50 rounded-lg">
                <span className="text-2xl">📊</span>
                <div>
                  <p className="font-medium text-sm">实时监控</p>
                  <p className="text-xs text-default-500">性能数据一目了然</p>
                </div>
              </div>
            </div>
          </CardBody>
          <CardFooter>
            <div className="flex gap-3">
              <Button
                color="primary"
                variant="solid"
                startContent={<span>🚀</span>}
                className="font-medium"
                as="a"
                href="/channels"
              >
                开始使用
              </Button>
              <Button
                color="default"
                variant="flat"
                startContent={<span>📖</span>}
                className="font-medium"
                as="a"
                href="http://localhost:8000/docs"
                target="_blank"
              >
                查看文档
              </Button>
            </div>
          </CardFooter>
        </Card>

        {/* 系统状态总览 */}
        <Card className="border-none shadow-lg bg-gradient-to-br from-success-50 to-success-100">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-success-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">🎯</span>
              </div>
              <div>
                <h4 className="font-bold text-large text-success-700">系统概览</h4>
                <p className="text-xs text-success-600">关键指标</p>
              </div>
            </div>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="text-center p-4 bg-white/50 rounded-lg">
                <div className="text-3xl font-bold text-success-700">99.9%</div>
                <div className="text-sm text-success-600">系统可用性</div>
              </div>
              <div className="text-center p-4 bg-white/50 rounded-lg">
                <div className="text-3xl font-bold text-success-700">&lt;100ms</div>
                <div className="text-sm text-success-600">平均响应时间</div>
              </div>
              <div className="text-center p-4 bg-white/50 rounded-lg">
                <div className="text-3xl font-bold text-success-700">24/7</div>
                <div className="text-sm text-success-600">服务时间</div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  )
}
