import { useState } from 'react'
import {
  <PERSON>,
  CardHeader,
  CardBody,
  Button,
  Chip,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Select,
  SelectItem,
  useDisclosure
} from "@heroui/react"

interface Channel {
  id: string
  name: string
  platform: string
  status: 'active' | 'inactive' | 'error'
  lastSync: string
}

const mockChannels: Channel[] = [
  {
    id: '1',
    name: '微信客服',
    platform: 'wechat',
    status: 'active',
    lastSync: '2025-01-15 10:30:00'
  },
  {
    id: '2',
    name: 'QQ客服',
    platform: 'qq',
    status: 'inactive',
    lastSync: '2025-01-14 15:20:00'
  },
  {
    id: '3',
    name: '钉钉客服',
    platform: 'dingtalk',
    status: 'error',
    lastSync: '2025-01-13 09:15:00'
  }
]

const platformOptions = [
  { key: 'wechat', label: '微信' },
  { key: 'qq', label: 'QQ' },
  { key: 'dingtalk', label: '钉钉' },
  { key: 'telegram', label: 'Telegram' },
  { key: 'discord', label: 'Discord' }
]

export function Channels() {
  const [channels, setChannels] = useState<Channel[]>(mockChannels)
  const { isOpen, onOpen, onOpenChange } = useDisclosure()
  const [newChannel, setNewChannel] = useState({
    name: '',
    platform: '',
    config: ''
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'warning'
      case 'error':
        return 'danger'
      default:
        return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '活跃'
      case 'inactive':
        return '未激活'
      case 'error':
        return '错误'
      default:
        return '未知'
    }
  }

  const handleAddChannel = () => {
    if (newChannel.name && newChannel.platform) {
      const channel: Channel = {
        id: Date.now().toString(),
        name: newChannel.name,
        platform: newChannel.platform,
        status: 'inactive',
        lastSync: new Date().toLocaleString()
      }
      setChannels([...channels, channel])
      setNewChannel({ name: '', platform: '', config: '' })
      onOpenChange()
    }
  }

  const handleDeleteChannel = (id: string) => {
    setChannels(channels.filter(channel => channel.id !== id))
  }

  const handleToggleStatus = (id: string) => {
    setChannels(channels.map(channel => 
      channel.id === id 
        ? { ...channel, status: channel.status === 'active' ? 'inactive' : 'active' as any }
        : channel
    ))
  }

  return (
    <div className="space-y-8">
      {/* 页面头部 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            渠道管理
          </h1>
          <p className="text-default-500 mt-1">管理和配置所有通信渠道</p>
        </div>
        <div className="flex gap-3">
          <Button
            color="default"
            variant="flat"
            startContent={<span>🔄</span>}
            className="font-medium"
          >
            刷新列表
          </Button>
          <Button
            color="primary"
            onPress={onOpen}
            startContent={<span>➕</span>}
            className="font-medium"
          >
            添加渠道
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-success-50 to-success-100">
          <CardBody className="text-center p-6">
            <div className="flex items-center justify-center gap-3 mb-2">
              <div className="w-10 h-10 bg-success-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">✅</span>
              </div>
              <div className="text-3xl font-bold text-success-700">
                {channels.filter(c => c.status === 'active').length}
              </div>
            </div>
            <div className="text-sm font-medium text-success-600">活跃渠道</div>
            <div className="text-xs text-success-500 mt-1">正常运行中</div>
          </CardBody>
        </Card>
        <Card className="border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-warning-50 to-warning-100">
          <CardBody className="text-center p-6">
            <div className="flex items-center justify-center gap-3 mb-2">
              <div className="w-10 h-10 bg-warning-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">⏸️</span>
              </div>
              <div className="text-3xl font-bold text-warning-700">
                {channels.filter(c => c.status === 'inactive').length}
              </div>
            </div>
            <div className="text-sm font-medium text-warning-600">未激活渠道</div>
            <div className="text-xs text-warning-500 mt-1">等待配置</div>
          </CardBody>
        </Card>
        <Card className="border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-danger-50 to-danger-100">
          <CardBody className="text-center p-6">
            <div className="flex items-center justify-center gap-3 mb-2">
              <div className="w-10 h-10 bg-danger-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-lg">❌</span>
              </div>
              <div className="text-3xl font-bold text-danger-700">
                {channels.filter(c => c.status === 'error').length}
              </div>
            </div>
            <div className="text-sm font-medium text-danger-600">错误渠道</div>
            <div className="text-xs text-danger-500 mt-1">需要修复</div>
          </CardBody>
        </Card>
      </div>

      {/* 渠道列表 */}
      <Card className="border-none shadow-lg">
        <CardHeader className="pb-4">
          <div className="flex justify-between items-center w-full">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm">📋</span>
              </div>
              <div>
                <h4 className="font-bold text-large">渠道列表</h4>
                <p className="text-xs text-default-500">共 {channels.length} 个渠道</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Input
                placeholder="搜索渠道..."
                startContent={<span>🔍</span>}
                className="w-64"
                size="sm"
              />
            </div>
          </div>
        </CardHeader>
        <CardBody className="p-0">
          <Table
            aria-label="渠道列表"
            className="min-h-[400px]"
            removeWrapper
          >
            <TableHeader>
              <TableColumn className="bg-default-100 text-default-700 font-semibold">
                <div className="flex items-center gap-2">
                  <span>📱</span>
                  <span>渠道信息</span>
                </div>
              </TableColumn>
              <TableColumn className="bg-default-100 text-default-700 font-semibold">
                <div className="flex items-center gap-2">
                  <span>🏷️</span>
                  <span>平台</span>
                </div>
              </TableColumn>
              <TableColumn className="bg-default-100 text-default-700 font-semibold">
                <div className="flex items-center gap-2">
                  <span>📊</span>
                  <span>状态</span>
                </div>
              </TableColumn>
              <TableColumn className="bg-default-100 text-default-700 font-semibold">
                <div className="flex items-center gap-2">
                  <span>🕒</span>
                  <span>最后同步</span>
                </div>
              </TableColumn>
              <TableColumn className="bg-default-100 text-default-700 font-semibold text-center">
                <div className="flex items-center gap-2 justify-center">
                  <span>⚙️</span>
                  <span>操作</span>
                </div>
              </TableColumn>
            </TableHeader>
            <TableBody>
              {channels.map((channel) => (
                <TableRow key={channel.id} className="hover:bg-default-50 transition-colors">
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${
                        channel.status === 'active' ? 'bg-success-500' :
                        channel.status === 'inactive' ? 'bg-warning-500' : 'bg-danger-500'
                      }`} />
                      <div>
                        <div className="font-medium text-default-800">{channel.name}</div>
                        <div className="text-xs text-default-500">ID: {channel.id}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Chip
                      variant="flat"
                      size="sm"
                      className="font-medium"
                      startContent={
                        <span className="text-sm">
                          {channel.platform === 'wechat' ? '💬' :
                           channel.platform === 'qq' ? '🐧' :
                           channel.platform === 'dingtalk' ? '📱' : '🌐'}
                        </span>
                      }
                    >
                      {platformOptions.find(p => p.key === channel.platform)?.label || channel.platform}
                    </Chip>
                  </TableCell>
                  <TableCell>
                    <Chip
                      color={getStatusColor(channel.status)}
                      variant="flat"
                      size="sm"
                      className="font-medium"
                      startContent={
                        <span className="text-xs">
                          {channel.status === 'active' ? '✅' :
                           channel.status === 'inactive' ? '⏸️' : '❌'}
                        </span>
                      }
                    >
                      {getStatusText(channel.status)}
                    </Chip>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-default-600">
                      {new Date(channel.lastSync).toLocaleString('zh-CN', {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2 justify-center">
                      <Button
                        size="sm"
                        color={channel.status === 'active' ? 'warning' : 'success'}
                        variant="flat"
                        onPress={() => handleToggleStatus(channel.id)}
                        className="font-medium min-w-16"
                        startContent={
                          <span className="text-xs">
                            {channel.status === 'active' ? '⏸️' : '▶️'}
                          </span>
                        }
                      >
                        {channel.status === 'active' ? '停用' : '启用'}
                      </Button>
                      <Button
                        size="sm"
                        color="danger"
                        variant="flat"
                        onPress={() => handleDeleteChannel(channel.id)}
                        className="font-medium"
                        startContent={<span className="text-xs">🗑️</span>}
                      >
                        删除
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* 添加渠道模态框 */}
      <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                添加新渠道
              </ModalHeader>
              <ModalBody>
                <Input
                  label="渠道名称"
                  placeholder="请输入渠道名称"
                  value={newChannel.name}
                  onValueChange={(value) => setNewChannel({...newChannel, name: value})}
                />
                <Select
                  label="选择平台"
                  placeholder="请选择平台"
                  selectedKeys={newChannel.platform ? [newChannel.platform] : []}
                  onSelectionChange={(keys) => {
                    const platform = Array.from(keys)[0] as string
                    setNewChannel({...newChannel, platform})
                  }}
                >
                  {platformOptions.map((platform) => (
                    <SelectItem key={platform.key}>
                      {platform.label}
                    </SelectItem>
                  ))}
                </Select>
                <Input
                  label="配置信息"
                  placeholder="请输入配置信息（可选）"
                  value={newChannel.config}
                  onValueChange={(value) => setNewChannel({...newChannel, config: value})}
                />
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  取消
                </Button>
                <Button color="primary" onPress={handleAddChannel}>
                  添加
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  )
}
