/**
 * API服务测试
 * 
 * 测试API客户端的功能
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import axios from 'axios'
import { createMockApiResponse, createMockErrorResponse } from '../test/utils'

// 模拟axios
vi.mock('axios')
const mockedAxios = vi.mocked(axios, true)

// 简单的API客户端示例
class ApiClient {
  private readonly baseURL: string
  
  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL
  }
  
  async get<T>(endpoint: string): Promise<T> {
    const response = await axios.get(`${this.baseURL}${endpoint}`)
    return response.data
  }
  
  async post<T>(endpoint: string, data: any): Promise<T> {
    const response = await axios.post(`${this.baseURL}${endpoint}`, data)
    return response.data
  }
  
  async healthCheck() {
    return this.get('/health')
  }
}

describe('ApiClient', () => {
  let apiClient: ApiClient
  
  beforeEach(() => {
    apiClient = new ApiClient()
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.resetAllMocks()
  })
  
  describe('GET请求', () => {
    it('应该成功发送GET请求', async () => {
      // Given: 模拟成功响应
      const mockData = { message: 'success' }
      vi.mocked(mockedAxios.get).mockResolvedValue(createMockApiResponse(mockData))

      // When: 发送GET请求
      const result = await apiClient.get<typeof mockData>('/test')

      // Then: 应该返回正确数据
      expect(result).toEqual(mockData)
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/test')
    })
    
    it('应该处理GET请求错误', async () => {
      // Given: 模拟错误响应
      const errorResponse = createMockErrorResponse('Not found', 404)
      vi.mocked(mockedAxios.get).mockRejectedValue(errorResponse)

      // When & Then: 发送GET请求应该抛出错误
      await expect(apiClient.get('/not-found')).rejects.toEqual(errorResponse)
    })
  })
  
  describe('POST请求', () => {
    it('应该成功发送POST请求', async () => {
      // Given: 模拟成功响应
      const mockData = { id: 1, name: 'test' }
      const postData = { name: 'test' }
      vi.mocked(mockedAxios.post).mockResolvedValue(createMockApiResponse(mockData))

      // When: 发送POST请求
      const result = await apiClient.post<typeof mockData>('/create', postData)

      // Then: 应该返回正确数据
      expect(result).toEqual(mockData)
      expect(mockedAxios.post).toHaveBeenCalledWith('/api/create', postData)
    })
  })
  
  describe('健康检查', () => {
    it('应该成功调用健康检查', async () => {
      // Given: 模拟健康检查响应
      const healthData = {
        status: 'healthy',
        app_name: '柴管家',
        version: '1.0.0'
      }
      vi.mocked(mockedAxios.get).mockResolvedValue(createMockApiResponse(healthData))

      // When: 调用健康检查
      const result = await apiClient.healthCheck()

      // Then: 应该返回健康状态
      expect(result).toEqual(healthData)
      expect((result as typeof healthData).status).toBe('healthy')
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/health')
    })
  })
})
