["tests/integration/test_database.py::TestDatabaseIntegration::test_database_connection", "tests/integration/test_database.py::TestDatabaseIntegration::test_database_transaction", "tests/unit/test_config.py::TestConfiguration::test_get_settings_returns_settings_instance", "tests/unit/test_config.py::TestConfiguration::test_settings_default_values", "tests/unit/test_config.py::TestConfiguration::test_settings_environment_specific", "tests/unit/test_config.py::TestConfiguration::test_settings_has_required_attributes", "tests/unit/test_health.py::TestApplicationConfig::test_settings_configuration", "tests/unit/test_health.py::TestHealthCheck::test_health_check_success", "tests/unit/test_health.py::TestHealthCheck::test_root_endpoint"]