"""
柴管家基础数据模型

定义所有模型的基类和通用字段
"""

import uuid
from datetime import datetime
from typing import Any, Optional, Union

from sqlalchemy import DateTime, String, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class BaseModel(DeclarativeBase):
    """
    所有数据模型的基类

    提供通用的配置和方法
    """

    # 类型注解映射
    type_annotation_map = {
        str: String(255),  # 默认字符串长度
    }

    def to_dict(self) -> dict[str, Any]:
        """
        将模型实例转换为字典

        Returns:
            dict[str, Any]: 模型数据字典
        """
        return {
            column.name: getattr(self, column.name) for column in self.__table__.columns
        }

    def update_from_dict(self, data: dict[str, Any]) -> None:
        """
        从字典更新模型实例

        Args:
            data: 更新数据字典
        """
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def __repr__(self) -> str:
        """
        模型的字符串表示

        Returns:
            str: 模型字符串表示
        """
        class_name = self.__class__.__name__

        # 尝试获取主键值
        primary_key_columns = [col for col in self.__table__.columns if col.primary_key]
        if primary_key_columns:
            pk_values = [
                f"{col.name}={getattr(self, col.name)}" for col in primary_key_columns
            ]
            return f"<{class_name}({', '.join(pk_values)})>"

        return f"<{class_name}()>"


class UUIDMixin:
    """
    UUID主键混入类

    为模型提供UUID类型的主键
    """

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment="主键ID"
    )


class TimestampMixin:
    """
    时间戳混入类

    为模型提供创建时间和更新时间字段
    """

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), comment="创建时间"
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )


class SoftDeleteMixin:
    """
    软删除混入类

    为模型提供软删除功能
    """

    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, comment="删除时间"
    )

    @property
    def is_deleted(self) -> bool:
        """
        检查是否已被软删除

        Returns:
            bool: 是否已删除
        """
        return self.deleted_at is not None

    def soft_delete(self) -> None:
        """
        执行软删除
        """
        self.deleted_at = datetime.utcnow()

    def restore(self) -> None:
        """
        恢复软删除的记录
        """
        self.deleted_at = None


class VersionMixin:
    """
    版本控制混入类

    为模型提供乐观锁版本控制
    """

    version: Mapped[int] = mapped_column(default=1, comment="版本号")

    def increment_version(self) -> None:
        """
        增加版本号
        """
        self.version += 1


class AuditMixin:
    """
    审计混入类

    为模型提供创建者和更新者信息
    """

    created_by: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, comment="创建者"
    )

    updated_by: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, comment="更新者"
    )
