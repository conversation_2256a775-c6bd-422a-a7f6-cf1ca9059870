"""
柴管家FastAPI应用主入口文件

本文件是整个后端应用的入口点，负责：
1. FastAPI应用实例化和配置
2. 中间件配置（CORS、日志、异常处理等）
3. 路由注册
4. 数据库连接初始化
5. 应用生命周期管理
"""

import logging
import sys
import time
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import structlog
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.api.v1.api import api_router
from app.core.config import get_settings
from app.core.database import database_manager
from app.core.exceptions import (BusinessLogicError, DatabaseError,
                                 ValidationError, global_exception_handler)
from app.core.logging import configure_logging
from app.core.middleware import (CacheControlMiddleware, LoggingMiddleware,
                                 ProcessTimeMiddleware, RateLimitMiddleware,
                                 SecurityHeadersMiddleware)

# 获取配置
settings = get_settings()

# 配置日志
configure_logging()
logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    应用生命周期管理

    在应用启动时初始化数据库连接等资源
    在应用关闭时清理资源
    """
    logger.info("应用启动中...", app_name=settings.app_name, version=settings.app_version)

    try:
        # 初始化数据库连接
        await database_manager.initialize()
        logger.info("数据库连接初始化成功")

        # 检查数据库连接
        await database_manager.health_check()
        logger.info("数据库健康检查通过")

        logger.info("应用启动完成", port=settings.port, debug=settings.debug)

        yield

    except Exception as e:
        logger.error("应用启动失败", error=str(e), exc_info=True)
        sys.exit(1)
    finally:
        # 清理资源
        logger.info("应用关闭中...")
        await database_manager.close()
        logger.info("应用关闭完成")


def create_application() -> FastAPI:
    """
    创建并配置FastAPI应用实例

    Returns:
        FastAPI: 配置完成的应用实例
    """

    # 创建FastAPI应用实例
    app = FastAPI(
        title=settings.app_name,
        description="多平台聚合智能客服系统 - 为知识类、教培类个人IP运营者提供一站式私域运营解决方案",
        version=settings.app_version,
        debug=settings.debug,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
        lifespan=lifespan,
    )

    # 配置中间件
    configure_middleware(app)

    # 配置异常处理
    configure_exception_handlers(app)

    # 注册路由
    configure_routes(app)

    return app


def configure_middleware(app: FastAPI) -> None:
    """
    配置应用中间件

    Args:
        app: FastAPI应用实例
    """

    # 信任主机中间件（安全）
    if not settings.debug:
        app.add_middleware(TrustedHostMiddleware, allowed_hosts=settings.allowed_hosts)

    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=settings.cors_allow_credentials,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
        allow_headers=["*"],
        expose_headers=["X-Process-Time", "X-Request-ID"],
    )

    # 自定义中间件
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(CacheControlMiddleware)
    app.add_middleware(ProcessTimeMiddleware)
    app.add_middleware(LoggingMiddleware)

    # 限流中间件（生产环境）
    if not settings.debug:
        app.add_middleware(RateLimitMiddleware)


def configure_exception_handlers(app: FastAPI) -> None:
    """
    配置异常处理器

    Args:
        app: FastAPI应用实例
    """

    # 业务逻辑异常
    @app.exception_handler(BusinessLogicError)
    async def business_logic_exception_handler(
        request: Request, exc: BusinessLogicError
    ):
        return JSONResponse(
            status_code=400,
            content={
                "error": "business_logic_error",
                "message": str(exc),
                "detail": exc.detail,
                "request_id": getattr(request.state, "request_id", None),
            },
        )

    # 数据库异常
    @app.exception_handler(DatabaseError)
    async def database_exception_handler(request: Request, exc: DatabaseError):
        logger.error("数据库错误", error=str(exc), exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": "database_error",
                "message": "数据库操作失败，请稍后重试",
                "request_id": getattr(request.state, "request_id", None),
            },
        )

    # 验证异常
    @app.exception_handler(ValidationError)
    async def validation_exception_handler(request: Request, exc: ValidationError):
        return JSONResponse(
            status_code=422,
            content={
                "error": "validation_error",
                "message": str(exc),
                "detail": exc.detail,
                "request_id": getattr(request.state, "request_id", None),
            },
        )

    # 全局异常处理器
    app.add_exception_handler(Exception, global_exception_handler)


def configure_routes(app: FastAPI) -> None:
    """
    配置应用路由

    Args:
        app: FastAPI应用实例
    """

    # 健康检查端点
    @app.get("/health", tags=["健康检查"])
    async def health_check():
        """
        健康检查端点

        Returns:
            dict: 健康状态信息
        """
        try:
            # 检查数据库连接
            db_healthy = await database_manager.health_check()

            return {
                "status": "healthy",
                "app_name": settings.app_name,
                "version": settings.app_version,
                "environment": settings.environment,
                "database": "healthy" if db_healthy else "unhealthy",
                "timestamp": time.time(),
            }
        except Exception as e:
            logger.error("健康检查失败", error=str(e))
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": structlog.get_logger().info("健康检查失败"),
                },
            )

    # 根路径
    @app.get("/", tags=["根路径"])
    async def root():
        """
        根路径端点

        Returns:
            dict: 应用基本信息
        """
        return {
            "message": f"欢迎使用{settings.app_name}",
            "version": settings.app_version,
            "docs_url": "/docs",
            "health_url": "/health",
            "api_url": "/api/v1",
        }

    # 注册API路由
    app.include_router(api_router, prefix="/api/v1")


# 创建应用实例
app = create_application()


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug",
        access_log=True,
    )
