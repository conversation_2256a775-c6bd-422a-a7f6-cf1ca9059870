"""
柴管家自定义异常类

定义应用中使用的各种异常类型，提供统一的错误处理机制
"""

import traceback
from typing import Any, Dict, Optional

import structlog
from fastapi import Request, status
from fastapi.responses import JSONResponse

logger = structlog.get_logger(__name__)


class BaseCustomException(Exception):
    """
    自定义异常基类

    所有业务异常都应继承此类
    """

    def __init__(
        self,
        message: str,
        detail: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
    ):
        self.message = message
        self.detail = detail or {}
        self.error_code = error_code
        super().__init__(self.message)


class ValidationError(BaseCustomException):
    """
    数据验证异常

    用于处理输入数据验证失败的情况
    """

    def __init__(
        self,
        message: str = "数据验证失败",
        detail: Optional[Dict[str, Any]] = None,
        field: Optional[str] = None,
    ):
        if field and detail is None:
            detail = {"field": field}
        super().__init__(message, detail, "VALIDATION_ERROR")


class BusinessLogicError(BaseCustomException):
    """
    业务逻辑异常

    用于处理业务规则违反的情况
    """

    def __init__(
        self, message: str = "业务逻辑错误", detail: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, detail, "BUSINESS_LOGIC_ERROR")


class DatabaseError(BaseCustomException):
    """
    数据库操作异常

    用于处理数据库相关错误
    """

    def __init__(
        self,
        message: str = "数据库操作失败",
        detail: Optional[Dict[str, Any]] = None,
        operation: Optional[str] = None,
    ):
        if operation and detail is None:
            detail = {"operation": operation}
        super().__init__(message, detail, "DATABASE_ERROR")


class AuthenticationError(BaseCustomException):
    """
    认证异常

    用于处理用户认证失败的情况
    """

    def __init__(self, message: str = "认证失败", detail: Optional[Dict[str, Any]] = None):
        super().__init__(message, detail, "AUTHENTICATION_ERROR")


class AuthorizationError(BaseCustomException):
    """
    授权异常

    用于处理用户权限不足的情况
    """

    def __init__(
        self,
        message: str = "权限不足",
        detail: Optional[Dict[str, Any]] = None,
        required_permission: Optional[str] = None,
    ):
        if required_permission and detail is None:
            detail = {"required_permission": required_permission}
        super().__init__(message, detail, "AUTHORIZATION_ERROR")


class NotFoundError(BaseCustomException):
    """
    资源不存在异常

    用于处理请求的资源不存在的情况
    """

    def __init__(
        self,
        message: str = "资源不存在",
        detail: Optional[Dict[str, Any]] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
    ):
        if resource_type or resource_id:
            detail = detail or {}
            if resource_type:
                detail["resource_type"] = resource_type
            if resource_id:
                detail["resource_id"] = resource_id
        super().__init__(message, detail, "NOT_FOUND_ERROR")


class ConflictError(BaseCustomException):
    """
    资源冲突异常

    用于处理资源冲突的情况（如重复创建）
    """

    def __init__(
        self,
        message: str = "资源冲突",
        detail: Optional[Dict[str, Any]] = None,
        conflict_field: Optional[str] = None,
    ):
        if conflict_field and detail is None:
            detail = {"conflict_field": conflict_field}
        super().__init__(message, detail, "CONFLICT_ERROR")


class ExternalServiceError(BaseCustomException):
    """
    外部服务异常

    用于处理外部服务调用失败的情况
    """

    def __init__(
        self,
        message: str = "外部服务调用失败",
        detail: Optional[Dict[str, Any]] = None,
        service_name: Optional[str] = None,
        status_code: Optional[int] = None,
    ):
        if service_name or status_code:
            detail = detail or {}
            if service_name:
                detail["service_name"] = service_name
            if status_code:
                detail["status_code"] = status_code
        super().__init__(message, detail, "EXTERNAL_SERVICE_ERROR")


class RateLimitError(BaseCustomException):
    """
    限流异常

    用于处理请求频率超限的情况
    """

    def __init__(
        self,
        message: str = "请求频率超限",
        detail: Optional[Dict[str, Any]] = None,
        retry_after: Optional[int] = None,
    ):
        if retry_after and detail is None:
            detail = {"retry_after": retry_after}
        super().__init__(message, detail, "RATE_LIMIT_ERROR")


class ConfigurationError(BaseCustomException):
    """
    配置异常

    用于处理配置错误的情况
    """

    def __init__(
        self,
        message: str = "配置错误",
        detail: Optional[Dict[str, Any]] = None,
        config_key: Optional[str] = None,
    ):
        if config_key and detail is None:
            detail = {"config_key": config_key}
        super().__init__(message, detail, "CONFIGURATION_ERROR")


async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    全局异常处理器

    处理所有未被特定处理器捕获的异常

    Args:
        request: FastAPI请求对象
        exc: 异常实例

    Returns:
        JSONResponse: 错误响应
    """

    # 获取请求ID（如果存在）
    request_id = getattr(request.state, "request_id", None)

    # 记录异常日志
    logger.error(
        "未处理的异常",
        error=str(exc),
        error_type=type(exc).__name__,
        request_id=request_id,
        path=request.url.path,
        method=request.method,
        exc_info=True,
    )

    # 根据异常类型返回不同的响应
    if isinstance(exc, BaseCustomException):
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "error": exc.error_code or "CUSTOM_ERROR",
                "message": exc.message,
                "detail": exc.detail,
                "request_id": request_id,
            },
        )

    # 对于未知异常，返回通用错误信息
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "INTERNAL_SERVER_ERROR",
            "message": "服务器内部错误，请稍后重试",
            "request_id": request_id,
        },
    )


def create_error_response(
    error_code: str,
    message: str,
    status_code: int = status.HTTP_400_BAD_REQUEST,
    detail: Optional[Dict[str, Any]] = None,
    request_id: Optional[str] = None,
) -> JSONResponse:
    """
    创建标准错误响应

    Args:
        error_code: 错误代码
        message: 错误消息
        status_code: HTTP状态码
        detail: 错误详情
        request_id: 请求ID

    Returns:
        JSONResponse: 错误响应
    """

    content = {
        "error": error_code,
        "message": message,
    }

    if detail:
        content["detail"] = detail

    if request_id:
        content["request_id"] = request_id

    return JSONResponse(status_code=status_code, content=content)
