"""
柴管家应用配置管理

使用Pydantic Settings进行配置管理，支持：
1. 环境变量读取
2. 配置验证
3. 类型转换
4. 默认值设置
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    应用配置类

    所有配置项都可以通过环境变量覆盖
    环境变量名格式：CHAIGUANJIA_{字段名大写}
    """

    # 应用基础配置
    app_name: str = Field(default="柴管家", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    environment: str = Field(default="development", description="运行环境")
    debug: bool = Field(default=True, description="调试模式")

    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8000, description="服务器端口")

    # 数据库配置
    database_url: str = Field(
        default="postgresql+asyncpg://chaiguanjia_user:chaiguanjia_password@localhost:5432/chaiguanjia_dev",
        description="数据库连接URL",
    )
    database_pool_size: int = Field(default=20, description="数据库连接池大小")
    database_max_overflow: int = Field(default=30, description="数据库连接池最大溢出")
    database_pool_timeout: int = Field(default=30, description="数据库连接池超时时间")
    database_pool_recycle: int = Field(default=3600, description="数据库连接回收时间")

    # Redis配置
    redis_url: str = Field(
        default="redis://:redis_password@localhost:6379/0", description="Redis连接URL"
    )
    redis_max_connections: int = Field(default=20, description="Redis最大连接数")

    # RabbitMQ配置
    rabbitmq_url: str = Field(
        default="amqp://admin:admin@localhost:5672/", description="RabbitMQ连接URL"
    )

    # ChromaDB配置
    chromadb_host: str = Field(default="localhost", description="ChromaDB主机")
    chromadb_port: int = Field(default=8001, description="ChromaDB端口")

    # 安全配置
    secret_key: str = Field(
        default="your-secret-key-change-in-production", description="JWT密钥"
    )
    algorithm: str = Field(default="HS256", description="JWT算法")
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间（分钟）")

    # CORS配置
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        description="允许的CORS源",
    )
    cors_allow_credentials: bool = Field(default=True, description="允许CORS凭据")

    # 信任主机配置
    allowed_hosts: List[str] = Field(
        default=["localhost", "127.0.0.1", "0.0.0.0"], description="允许的主机"
    )

    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(default="json", description="日志格式")
    log_file: Optional[str] = Field(default=None, description="日志文件路径")

    # API配置
    api_v1_prefix: str = Field(default="/api/v1", description="API v1前缀")
    max_request_size: int = Field(default=16 * 1024 * 1024, description="最大请求大小（字节）")

    # 限流配置
    rate_limit_requests: int = Field(default=100, description="限流请求数")
    rate_limit_window: int = Field(default=60, description="限流时间窗口（秒）")

    # AI服务配置
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API密钥")
    openai_base_url: Optional[str] = Field(default=None, description="OpenAI API基础URL")
    ai_model_name: str = Field(default="gpt-3.5-turbo", description="AI模型名称")
    ai_max_tokens: int = Field(default=1000, description="AI最大令牌数")
    ai_temperature: float = Field(default=0.7, description="AI温度参数")

    # 文件上传配置
    upload_max_size: int = Field(default=10 * 1024 * 1024, description="上传文件最大大小（字节）")
    upload_allowed_types: List[str] = Field(
        default=["image/jpeg", "image/png", "image/gif", "application/pdf"],
        description="允许的上传文件类型",
    )

    # 缓存配置
    cache_ttl: int = Field(default=300, description="缓存TTL（秒）")
    cache_max_size: int = Field(default=1000, description="缓存最大条目数")

    # 任务队列配置
    celery_broker_url: str = Field(
        default="redis://:redis_password@localhost:6379/1", description="Celery代理URL"
    )
    celery_result_backend: str = Field(
        default="redis://:redis_password@localhost:6379/2", description="Celery结果后端URL"
    )

    # 监控配置
    enable_metrics: bool = Field(default=True, description="启用指标收集")
    metrics_port: int = Field(default=9090, description="指标端口")

    @validator("environment")
    def validate_environment(cls, v):
        """验证环境配置"""
        allowed_envs = ["development", "testing", "staging", "production"]
        if v not in allowed_envs:
            raise ValueError(f"环境必须是以下之一: {allowed_envs}")
        return v

    @validator("log_level")
    def validate_log_level(cls, v):
        """验证日志级别"""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"日志级别必须是以下之一: {allowed_levels}")
        return v.upper()

    @validator("cors_origins", pre=True)
    def parse_cors_origins(cls, v):
        """解析CORS源配置"""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v

    @validator("allowed_hosts", pre=True)
    def parse_allowed_hosts(cls, v):
        """解析允许主机配置"""
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v

    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment == "development"

    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment == "production"

    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.environment == "testing"

    class Config:
        env_prefix = "CHAIGUANJIA_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """
    获取应用配置实例（单例模式）

    Returns:
        Settings: 配置实例
    """
    return Settings()
