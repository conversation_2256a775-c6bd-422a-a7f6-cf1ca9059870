"""
柴管家自定义中间件

提供请求处理、日志记录、性能监控等中间件功能
"""

import time
import uuid
from typing import Callable

import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from app.core.config import get_settings
from app.core.exceptions import RateLimitError

settings = get_settings()
logger = structlog.get_logger(__name__)


class ProcessTimeMiddleware(BaseHTTPMiddleware):
    """
    请求处理时间中间件

    记录每个请求的处理时间并添加到响应头
    """

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求并记录处理时间

        Args:
            request: 请求对象
            call_next: 下一个处理器

        Returns:
            Response: 响应对象
        """

        start_time = time.time()

        # 处理请求
        response = await call_next(request)

        # 计算处理时间
        process_time = time.time() - start_time

        # 添加处理时间到响应头
        response.headers["X-Process-Time"] = str(round(process_time * 1000, 2))

        return response


class LoggingMiddleware(BaseHTTPMiddleware):
    """
    请求日志中间件

    记录所有HTTP请求和响应的详细信息
    """

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        记录请求和响应日志

        Args:
            request: 请求对象
            call_next: 下一个处理器

        Returns:
            Response: 响应对象
        """

        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id

        # 记录请求开始
        start_time = time.time()

        # 获取客户端IP
        client_ip = self._get_client_ip(request)

        # 记录请求日志
        logger.info(
            "HTTP请求开始",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            path=request.url.path,
            query_params=dict(request.query_params),
            client_ip=client_ip,
            user_agent=request.headers.get("user-agent"),
        )

        try:
            # 处理请求
            response = await call_next(request)

            # 计算处理时间
            process_time = time.time() - start_time

            # 添加请求ID到响应头
            response.headers["X-Request-ID"] = request_id

            # 记录响应日志
            logger.info(
                "HTTP请求完成",
                request_id=request_id,
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                process_time_ms=round(process_time * 1000, 2),
                response_size=response.headers.get("content-length"),
            )

            return response

        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time

            # 记录错误日志
            logger.error(
                "HTTP请求异常",
                request_id=request_id,
                method=request.method,
                path=request.url.path,
                error=str(e),
                error_type=type(e).__name__,
                process_time_ms=round(process_time * 1000, 2),
                exc_info=True,
            )

            raise

    def _get_client_ip(self, request: Request) -> str:
        """
        获取客户端真实IP地址

        Args:
            request: 请求对象

        Returns:
            str: 客户端IP地址
        """

        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # 返回直接连接的IP
        return request.client.host if request.client else "unknown"


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    限流中间件

    基于IP地址进行请求频率限制
    """

    def __init__(
        self, app, requests_per_minute: int = None, window_seconds: int = None
    ):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute or settings.rate_limit_requests
        self.window_seconds = window_seconds or settings.rate_limit_window
        self.request_counts = {}  # 简单的内存存储，生产环境应使用Redis

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        检查请求频率限制

        Args:
            request: 请求对象
            call_next: 下一个处理器

        Returns:
            Response: 响应对象
        """

        # 跳过健康检查端点
        if request.url.path in ["/health", "/docs", "/openapi.json"]:
            return await call_next(request)

        # 获取客户端IP
        client_ip = self._get_client_ip(request)
        current_time = time.time()

        # 清理过期记录
        self._cleanup_expired_records(current_time)

        # 检查当前IP的请求次数
        if client_ip in self.request_counts:
            request_times = self.request_counts[client_ip]

            # 计算时间窗口内的请求次数
            recent_requests = [
                req_time
                for req_time in request_times
                if current_time - req_time < self.window_seconds
            ]

            if len(recent_requests) >= self.requests_per_minute:
                # 超过限制
                logger.warning(
                    "请求频率超限",
                    client_ip=client_ip,
                    requests_count=len(recent_requests),
                    limit=self.requests_per_minute,
                    window_seconds=self.window_seconds,
                )

                return JSONResponse(
                    status_code=429,
                    content={
                        "error": "RATE_LIMIT_EXCEEDED",
                        "message": "请求频率超限，请稍后重试",
                        "retry_after": self.window_seconds,
                    },
                    headers={"Retry-After": str(self.window_seconds)},
                )

            # 更新请求记录
            self.request_counts[client_ip] = recent_requests + [current_time]
        else:
            # 首次请求
            self.request_counts[client_ip] = [current_time]

        return await call_next(request)

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        return request.client.host if request.client else "unknown"

    def _cleanup_expired_records(self, current_time: float) -> None:
        """清理过期的请求记录"""
        expired_ips = []

        for ip, request_times in self.request_counts.items():
            # 过滤掉过期的请求时间
            recent_requests = [
                req_time
                for req_time in request_times
                if current_time - req_time < self.window_seconds
            ]

            if recent_requests:
                self.request_counts[ip] = recent_requests
            else:
                expired_ips.append(ip)

        # 删除没有近期请求的IP记录
        for ip in expired_ips:
            del self.request_counts[ip]


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    安全头中间件

    添加安全相关的HTTP响应头
    """

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        添加安全响应头

        Args:
            request: 请求对象
            call_next: 下一个处理器

        Returns:
            Response: 响应对象
        """

        response = await call_next(request)

        # 添加安全头
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Content-Security-Policy": "default-src 'self'",
        }

        for header, value in security_headers.items():
            response.headers[header] = value

        return response


class CacheControlMiddleware(BaseHTTPMiddleware):
    """
    缓存控制中间件

    为静态资源和API响应添加适当的缓存头
    """

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        添加缓存控制头

        Args:
            request: 请求对象
            call_next: 下一个处理器

        Returns:
            Response: 响应对象
        """

        response = await call_next(request)

        # 根据路径设置缓存策略
        path = request.url.path

        if path.startswith("/static/"):
            # 静态资源长期缓存
            response.headers["Cache-Control"] = "public, max-age=31536000"
        elif path.startswith("/api/"):
            # API响应不缓存
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
        elif path in ["/health", "/docs", "/openapi.json"]:
            # 健康检查和文档短期缓存
            response.headers["Cache-Control"] = "public, max-age=300"

        return response
