"""
柴管家日志配置

使用structlog提供结构化日志记录，支持：
1. JSON格式输出
2. 上下文信息记录
3. 性能监控
4. 错误追踪
"""

import logging
import sys
from typing import Any, Dict

import structlog
from pythonjsonlogger import jsonlogger

from app.core.config import get_settings

settings = get_settings()


def configure_logging() -> None:
    """
    配置应用日志系统

    设置structlog和标准logging的配置
    """

    # 配置标准logging
    configure_standard_logging()

    # 配置structlog
    configure_structlog()


def configure_standard_logging() -> None:
    """
    配置标准Python logging
    """

    # 设置日志级别
    log_level = getattr(logging, settings.log_level.upper())

    # 创建格式化器
    if settings.log_format == "json":
        formatter = jsonlogger.JsonFormatter(
            fmt="%(asctime)s %(name)s %(levelname)s %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
    else:
        formatter = logging.Formatter(
            fmt="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 添加控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 添加文件处理器（如果配置了日志文件）
    if settings.log_file:
        file_handler = logging.FileHandler(settings.log_file)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    # 配置第三方库日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)


def configure_structlog() -> None:
    """
    配置structlog结构化日志
    """

    # 定义处理器链
    processors = [
        # 添加时间戳
        structlog.stdlib.add_log_level,
        structlog.stdlib.add_logger_name,
        structlog.processors.TimeStamper(fmt="iso"),
        # 添加调用信息
        structlog.processors.CallsiteParameterAdder(
            parameters=[
                structlog.processors.CallsiteParameter.FILENAME,
                structlog.processors.CallsiteParameter.FUNC_NAME,
                structlog.processors.CallsiteParameter.LINENO,
            ]
        ),
        # 添加应用信息
        add_app_context,
        # 格式化异常
        structlog.processors.format_exc_info,
        # 处理字典展开
        structlog.processors.UnicodeDecoder(),
    ]

    # 根据配置选择渲染器
    if settings.log_format == "json":
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.append(structlog.dev.ConsoleRenderer(colors=True))

    # 配置structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )


def add_app_context(
    logger: Any, method_name: str, event_dict: Dict[str, Any]
) -> Dict[str, Any]:
    """
    添加应用上下文信息到日志

    Args:
        logger: 日志记录器
        method_name: 方法名
        event_dict: 事件字典

    Returns:
        Dict[str, Any]: 更新后的事件字典
    """

    event_dict.update(
        {
            "app_name": settings.app_name,
            "app_version": settings.app_version,
            "environment": settings.environment,
        }
    )

    return event_dict


def get_logger(name: str = None) -> structlog.BoundLogger:
    """
    获取结构化日志记录器

    Args:
        name: 日志记录器名称

    Returns:
        structlog.BoundLogger: 日志记录器实例
    """

    return structlog.get_logger(name)


class LoggingMixin:
    """
    日志记录混入类

    为类提供便捷的日志记录功能
    """

    @property
    def logger(self) -> structlog.BoundLogger:
        """获取类专用的日志记录器"""
        return get_logger(self.__class__.__name__)


def log_function_call(func_name: str, **kwargs) -> None:
    """
    记录函数调用日志

    Args:
        func_name: 函数名
        **kwargs: 函数参数
    """

    logger = get_logger("function_call")
    logger.info(f"调用函数: {func_name}", **kwargs)


def log_api_request(method: str, path: str, **kwargs) -> None:
    """
    记录API请求日志

    Args:
        method: HTTP方法
        path: 请求路径
        **kwargs: 额外信息
    """

    logger = get_logger("api_request")
    logger.info(f"API请求: {method} {path}", **kwargs)


def log_api_response(
    method: str, path: str, status_code: int, duration: float, **kwargs
) -> None:
    """
    记录API响应日志

    Args:
        method: HTTP方法
        path: 请求路径
        status_code: 状态码
        duration: 处理时间
        **kwargs: 额外信息
    """

    logger = get_logger("api_response")
    logger.info(
        f"API响应: {method} {path}",
        status_code=status_code,
        duration_ms=round(duration * 1000, 2),
        **kwargs,
    )


def log_database_operation(operation: str, table: str, **kwargs) -> None:
    """
    记录数据库操作日志

    Args:
        operation: 操作类型
        table: 表名
        **kwargs: 额外信息
    """

    logger = get_logger("database")
    logger.info(f"数据库操作: {operation} {table}", **kwargs)


def log_external_service_call(service: str, endpoint: str, **kwargs) -> None:
    """
    记录外部服务调用日志

    Args:
        service: 服务名
        endpoint: 端点
        **kwargs: 额外信息
    """

    logger = get_logger("external_service")
    logger.info(f"外部服务调用: {service} {endpoint}", **kwargs)


def log_business_event(event: str, **kwargs) -> None:
    """
    记录业务事件日志

    Args:
        event: 事件名
        **kwargs: 事件数据
    """

    logger = get_logger("business_event")
    logger.info(f"业务事件: {event}", **kwargs)


def log_security_event(event: str, **kwargs) -> None:
    """
    记录安全事件日志

    Args:
        event: 事件名
        **kwargs: 事件数据
    """

    logger = get_logger("security")
    logger.warning(f"安全事件: {event}", **kwargs)


def log_performance_metric(
    metric: str, value: float, unit: str = "ms", **kwargs
) -> None:
    """
    记录性能指标日志

    Args:
        metric: 指标名
        value: 指标值
        unit: 单位
        **kwargs: 额外信息
    """

    logger = get_logger("performance")
    logger.info(f"性能指标: {metric}", value=value, unit=unit, **kwargs)
