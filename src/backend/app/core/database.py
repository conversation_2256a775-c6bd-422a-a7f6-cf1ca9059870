"""
柴管家数据库连接管理

提供数据库连接池管理、会话管理和健康检查功能
使用SQLAlchemy 2.0异步API
"""

import asyncio
from typing import AsyncGenerator, Optional

import structlog
from sqlalchemy import text
from sqlalchemy.ext.asyncio import (AsyncEngine, AsyncSession,
                                    async_sessionmaker, create_async_engine)
from sqlalchemy.orm import DeclarativeBase

from app.core.config import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()


class Base(DeclarativeBase):
    """
    SQLAlchemy声明式基类

    所有数据库模型都应继承此类
    """

    pass


class DatabaseManager:
    """
    数据库连接管理器

    负责管理数据库连接池、会话创建和生命周期管理
    """

    def __init__(self):
        self._engine: Optional[AsyncEngine] = None
        self._session_factory: Optional[async_sessionmaker[AsyncSession]] = None
        self._initialized = False

    async def initialize(self) -> None:
        """
        初始化数据库连接

        创建连接池和会话工厂
        """
        if self._initialized:
            logger.warning("数据库已经初始化，跳过重复初始化")
            return

        try:
            logger.info("初始化数据库连接", database_url=settings.database_url.split("@")[1])

            # 创建异步引擎
            self._engine = create_async_engine(
                settings.database_url,
                pool_size=settings.database_pool_size,
                max_overflow=settings.database_max_overflow,
                pool_timeout=settings.database_pool_timeout,
                pool_recycle=settings.database_pool_recycle,
                echo=settings.debug,  # 在调试模式下打印SQL
                future=True,
            )

            # 创建会话工厂
            self._session_factory = async_sessionmaker(
                bind=self._engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False,
            )

            self._initialized = True
            logger.info("数据库连接初始化成功")

        except Exception as e:
            logger.error("数据库连接初始化失败", error=str(e), exc_info=True)
            raise

    async def close(self) -> None:
        """
        关闭数据库连接

        清理连接池和相关资源
        """
        if not self._initialized:
            return

        try:
            logger.info("关闭数据库连接")

            if self._engine:
                await self._engine.dispose()
                self._engine = None

            self._session_factory = None
            self._initialized = False

            logger.info("数据库连接关闭成功")

        except Exception as e:
            logger.error("数据库连接关闭失败", error=str(e), exc_info=True)

    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        获取数据库会话

        使用异步上下文管理器确保会话正确关闭

        Yields:
            AsyncSession: 数据库会话
        """
        if not self._initialized or not self._session_factory:
            raise RuntimeError("数据库未初始化，请先调用initialize()")

        async with self._session_factory() as session:
            try:
                yield session
            except Exception as e:
                logger.error("数据库会话错误", error=str(e), exc_info=True)
                await session.rollback()
                raise
            finally:
                await session.close()

    async def health_check(self) -> bool:
        """
        数据库健康检查

        Returns:
            bool: 数据库是否健康
        """
        if not self._initialized or not self._engine:
            logger.warning("数据库未初始化，健康检查失败")
            return False

        try:
            async with self._engine.begin() as conn:
                result = await conn.execute(text("SELECT 1"))
                row = result.fetchone()

                if row and row[0] == 1:
                    logger.debug("数据库健康检查通过")
                    return True
                else:
                    logger.warning("数据库健康检查失败：查询结果异常")
                    return False

        except Exception as e:
            logger.error("数据库健康检查失败", error=str(e), exc_info=True)
            return False

    async def execute_raw_sql(self, sql: str, params: Optional[dict] = None) -> any:
        """
        执行原始SQL语句

        Args:
            sql: SQL语句
            params: 参数字典

        Returns:
            查询结果
        """
        if not self._initialized or not self._engine:
            raise RuntimeError("数据库未初始化，请先调用initialize()")

        try:
            async with self._engine.begin() as conn:
                result = await conn.execute(text(sql), params or {})
                return result

        except Exception as e:
            logger.error("执行原始SQL失败", sql=sql, error=str(e), exc_info=True)
            raise

    @property
    def engine(self) -> Optional[AsyncEngine]:
        """获取数据库引擎"""
        return self._engine

    @property
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self._initialized


# 全局数据库管理器实例
database_manager = DatabaseManager()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI依赖注入函数：获取数据库会话

    用于在API端点中注入数据库会话

    Yields:
        AsyncSession: 数据库会话
    """
    async for session in database_manager.get_session():
        yield session


async def create_tables() -> None:
    """
    创建所有数据库表

    在开发环境中使用，生产环境应使用Alembic迁移
    """
    if not database_manager.is_initialized:
        await database_manager.initialize()

    try:
        logger.info("创建数据库表")

        async with database_manager.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        logger.info("数据库表创建成功")

    except Exception as e:
        logger.error("数据库表创建失败", error=str(e), exc_info=True)
        raise


async def drop_tables() -> None:
    """
    删除所有数据库表

    仅在测试环境中使用
    """
    if not database_manager.is_initialized:
        await database_manager.initialize()

    try:
        logger.warning("删除数据库表")

        async with database_manager.engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)

        logger.warning("数据库表删除成功")

    except Exception as e:
        logger.error("数据库表删除失败", error=str(e), exc_info=True)
        raise


# 用于测试的数据库会话
async def get_test_db() -> AsyncGenerator[AsyncSession, None]:
    """
    测试用数据库会话

    每个测试用例都会获得一个独立的事务，测试结束后回滚

    Yields:
        AsyncSession: 测试数据库会话
    """
    async for session in database_manager.get_session():
        transaction = await session.begin()
        try:
            yield session
        finally:
            await transaction.rollback()
            await session.close()
