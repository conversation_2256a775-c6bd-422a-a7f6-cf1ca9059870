"""
系统信息API端点

提供应用系统信息和配置查询
"""

import os
import platform
import sys
import time
from typing import Any, Dict

import psutil
import structlog
from fastapi import APIRouter

from app.core.config import get_settings

router = APIRouter()
logger = structlog.get_logger(__name__)
settings = get_settings()


@router.get("/info", summary="系统基本信息")
async def get_system_info() -> Dict[str, Any]:
    """
    获取系统基本信息

    Returns:
        Dict[str, Any]: 系统信息
    """

    return {
        "app": {
            "name": settings.app_name,
            "version": settings.app_version,
            "environment": settings.environment,
            "debug": settings.debug,
        },
        "python": {
            "version": sys.version,
            "executable": sys.executable,
            "platform": platform.platform(),
        },
        "system": {
            "os": platform.system(),
            "release": platform.release(),
            "machine": platform.machine(),
            "processor": platform.processor(),
        },
        "timestamp": time.time(),
    }


@router.get("/metrics", summary="系统性能指标")
async def get_system_metrics() -> Dict[str, Any]:
    """
    获取系统性能指标

    Returns:
        Dict[str, Any]: 性能指标
    """

    # 获取CPU信息
    cpu_percent = psutil.cpu_percent(interval=1)
    cpu_count = psutil.cpu_count()
    try:
        cpu_freq = psutil.cpu_freq()
    except (FileNotFoundError, OSError):
        cpu_freq = None

    # 获取内存信息
    memory = psutil.virtual_memory()

    # 获取磁盘信息
    disk = psutil.disk_usage("/")

    # 获取网络信息
    network = psutil.net_io_counters()

    # 获取进程信息
    process = psutil.Process()
    process_memory = process.memory_info()

    return {
        "cpu": {
            "percent": cpu_percent,
            "count": cpu_count,
            "frequency_mhz": cpu_freq.current if cpu_freq else None,
        },
        "memory": {
            "total_bytes": memory.total,
            "available_bytes": memory.available,
            "used_bytes": memory.used,
            "percent": memory.percent,
        },
        "disk": {
            "total_bytes": disk.total,
            "used_bytes": disk.used,
            "free_bytes": disk.free,
            "percent": (disk.used / disk.total) * 100,
        },
        "network": {
            "bytes_sent": network.bytes_sent,
            "bytes_recv": network.bytes_recv,
            "packets_sent": network.packets_sent,
            "packets_recv": network.packets_recv,
        },
        "process": {
            "pid": process.pid,
            "memory_rss_bytes": process_memory.rss,
            "memory_vms_bytes": process_memory.vms,
            "cpu_percent": process.cpu_percent(),
            "num_threads": process.num_threads(),
        },
        "timestamp": time.time(),
    }


@router.get("/config", summary="应用配置信息")
async def get_config_info() -> Dict[str, Any]:
    """
    获取应用配置信息（脱敏后）

    Returns:
        Dict[str, Any]: 配置信息
    """

    # 脱敏配置信息
    config_info = {
        "app": {
            "name": settings.app_name,
            "version": settings.app_version,
            "environment": settings.environment,
            "debug": settings.debug,
        },
        "server": {
            "host": settings.host,
            "port": settings.port,
        },
        "database": {
            "pool_size": settings.database_pool_size,
            "max_overflow": settings.database_max_overflow,
            "pool_timeout": settings.database_pool_timeout,
            "pool_recycle": settings.database_pool_recycle,
        },
        "api": {
            "v1_prefix": settings.api_v1_prefix,
            "max_request_size": settings.max_request_size,
        },
        "rate_limit": {
            "requests": settings.rate_limit_requests,
            "window_seconds": settings.rate_limit_window,
        },
        "cors": {
            "origins": settings.cors_origins,
            "allow_credentials": settings.cors_allow_credentials,
        },
        "logging": {
            "level": settings.log_level,
            "format": settings.log_format,
        },
        "cache": {
            "ttl": settings.cache_ttl,
            "max_size": settings.cache_max_size,
        },
        "upload": {
            "max_size": settings.upload_max_size,
            "allowed_types": settings.upload_allowed_types,
        },
        "ai": {
            "model_name": settings.ai_model_name,
            "max_tokens": settings.ai_max_tokens,
            "temperature": settings.ai_temperature,
        },
        "monitoring": {
            "enable_metrics": settings.enable_metrics,
            "metrics_port": settings.metrics_port,
        },
    }

    return config_info


@router.get("/environment", summary="环境变量信息")
async def get_environment_info() -> Dict[str, Any]:
    """
    获取环境变量信息（脱敏后）

    Returns:
        Dict[str, Any]: 环境变量信息
    """

    # 获取所有环境变量
    env_vars = dict(os.environ)

    # 脱敏敏感信息
    sensitive_keys = [
        "password",
        "secret",
        "key",
        "token",
        "credential",
        "api_key",
        "private",
        "auth",
        "cert",
        "ssl",
    ]

    filtered_env = {}
    for key, value in env_vars.items():
        # 检查是否为敏感信息
        is_sensitive = any(sensitive in key.lower() for sensitive in sensitive_keys)

        if is_sensitive:
            filtered_env[key] = "***HIDDEN***"
        else:
            filtered_env[key] = value

    return {
        "environment_variables": filtered_env,
        "total_count": len(env_vars),
        "filtered_count": len(
            [k for k in env_vars.keys() if any(s in k.lower() for s in sensitive_keys)]
        ),
    }


@router.get("/dependencies", summary="依赖包信息")
async def get_dependencies_info() -> Dict[str, Any]:
    """
    获取Python依赖包信息

    Returns:
        Dict[str, Any]: 依赖包信息
    """

    try:
        import pkg_resources

        installed_packages = []
        for package in pkg_resources.working_set:
            installed_packages.append(
                {
                    "name": package.project_name,
                    "version": package.version,
                    "location": package.location,
                }
            )

        # 按名称排序
        installed_packages.sort(key=lambda x: x["name"].lower())

        return {
            "packages": installed_packages,
            "total_count": len(installed_packages),
            "python_version": sys.version,
        }

    except Exception as e:
        logger.error("获取依赖包信息失败", error=str(e))
        return {
            "error": "无法获取依赖包信息",
            "message": str(e),
        }
