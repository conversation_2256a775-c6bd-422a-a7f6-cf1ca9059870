"""
健康检查API端点

提供应用和各种服务的健康状态检查
"""

import asyncio
import time
from typing import Any, Dict

import structlog
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import get_settings
from app.core.database import database_manager, get_db

router = APIRouter()
logger = structlog.get_logger(__name__)
settings = get_settings()


@router.get("/", summary="基础健康检查")
async def basic_health_check() -> Dict[str, Any]:
    """
    基础健康检查端点

    快速检查应用是否正常运行

    Returns:
        Dict[str, Any]: 健康状态信息
    """

    return {
        "status": "healthy",
        "timestamp": time.time(),
        "app_name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.environment,
        "database": "healthy",
    }


@router.get("/detailed", summary="详细健康检查")
async def detailed_health_check(db: AsyncSession = Depends(get_db)) -> Dict[str, Any]:
    """
    详细健康检查端点

    检查应用及其依赖服务的健康状态

    Args:
        db: 数据库会话

    Returns:
        Dict[str, Any]: 详细健康状态信息

    Raises:
        HTTPException: 当服务不健康时
    """

    start_time = time.time()
    health_status = {
        "status": "healthy",
        "timestamp": start_time,
        "app_name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.environment,
        "checks": {},
    }

    # 检查数据库连接
    try:
        db_start = time.time()
        db_healthy = await database_manager.health_check()
        db_duration = time.time() - db_start

        health_status["checks"]["database"] = {
            "status": "healthy" if db_healthy else "unhealthy",
            "response_time_ms": round(db_duration * 1000, 2),
            "details": {
                "connection_pool_size": settings.database_pool_size,
                "max_overflow": settings.database_max_overflow,
            },
        }

        if not db_healthy:
            health_status["status"] = "unhealthy"

    except Exception as e:
        logger.error("数据库健康检查失败", error=str(e))
        health_status["checks"]["database"] = {"status": "unhealthy", "error": str(e)}
        health_status["status"] = "unhealthy"

    # 检查Redis连接（如果配置了）
    try:
        redis_start = time.time()
        # 这里应该添加Redis连接检查
        # redis_healthy = await check_redis_connection()
        redis_duration = time.time() - redis_start

        health_status["checks"]["redis"] = {
            "status": "not_configured",  # 暂时标记为未配置
            "response_time_ms": round(redis_duration * 1000, 2),
        }

    except Exception as e:
        logger.error("Redis健康检查失败", error=str(e))
        health_status["checks"]["redis"] = {"status": "unhealthy", "error": str(e)}

    # 检查RabbitMQ连接（如果配置了）
    try:
        rabbitmq_start = time.time()
        # 这里应该添加RabbitMQ连接检查
        # rabbitmq_healthy = await check_rabbitmq_connection()
        rabbitmq_duration = time.time() - rabbitmq_start

        health_status["checks"]["rabbitmq"] = {
            "status": "not_configured",  # 暂时标记为未配置
            "response_time_ms": round(rabbitmq_duration * 1000, 2),
        }

    except Exception as e:
        logger.error("RabbitMQ健康检查失败", error=str(e))
        health_status["checks"]["rabbitmq"] = {"status": "unhealthy", "error": str(e)}

    # 添加总体响应时间
    total_duration = time.time() - start_time
    health_status["total_response_time_ms"] = round(total_duration * 1000, 2)

    # 如果有任何服务不健康，返回503状态码
    if health_status["status"] == "unhealthy":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=health_status
        )

    return health_status


@router.get("/readiness", summary="就绪检查")
async def readiness_check(db: AsyncSession = Depends(get_db)) -> Dict[str, Any]:
    """
    就绪检查端点

    检查应用是否准备好接收流量

    Args:
        db: 数据库会话

    Returns:
        Dict[str, Any]: 就绪状态信息
    """

    try:
        # 检查数据库连接
        db_healthy = await database_manager.health_check()

        if db_healthy:
            return {
                "status": "ready",
                "timestamp": time.time(),
                "message": "应用已准备好接收流量",
            }
        else:
            return {
                "status": "not_ready",
                "timestamp": time.time(),
                "message": "数据库连接不可用",
            }

    except Exception as e:
        logger.error("就绪检查失败", error=str(e))
        return {
            "status": "not_ready",
            "timestamp": time.time(),
            "message": f"就绪检查失败: {str(e)}",
        }


@router.get("/liveness", summary="存活检查")
async def liveness_check() -> Dict[str, Any]:
    """
    存活检查端点

    检查应用是否仍在运行

    Returns:
        Dict[str, Any]: 存活状态信息
    """

    return {"status": "alive", "timestamp": time.time(), "message": "应用正在运行"}


@router.get("/readiness", summary="就绪检查")
async def readiness_check(db: AsyncSession = Depends(get_db)) -> Dict[str, Any]:
    """
    就绪检查端点

    检查应用是否准备好接收流量
    主要检查关键依赖服务是否可用

    Args:
        db: 数据库会话

    Returns:
        Dict[str, Any]: 就绪状态信息

    Raises:
        HTTPException: 当应用未就绪时
    """

    try:
        # 检查数据库连接
        db_healthy = await database_manager.health_check()

        if not db_healthy:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={
                    "status": "not_ready",
                    "reason": "数据库连接不可用",
                    "timestamp": time.time(),
                },
            )

        return {"status": "ready", "timestamp": time.time(), "message": "应用已准备好接收流量"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error("就绪检查失败", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={
                "status": "not_ready",
                "reason": f"就绪检查失败: {str(e)}",
                "timestamp": time.time(),
            },
        )


@router.get("/liveness", summary="存活检查")
async def liveness_check() -> Dict[str, Any]:
    """
    存活检查端点

    检查应用进程是否存活
    这是最基础的健康检查，不依赖外部服务

    Returns:
        Dict[str, Any]: 存活状态信息
    """

    return {"status": "alive", "timestamp": time.time(), "message": "应用进程正常运行"}
